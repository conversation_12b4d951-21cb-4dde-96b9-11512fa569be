# 多产品标签打印功能说明

## 功能概述

多产品标签打印功能是对原有简单标签打印功能的全面升级，支持从入库单中选择多个产品进行批量标签打印，提供了更灵活的产品选择、数量设置和字段自定义功能。

## 主要特性

### 1. 多产品选择
- ✅ 显示入库单中的所有产品列表
- ✅ 支持单个产品选择/取消选择
- ✅ 支持全选/全不选操作
- ✅ 支持批量操作工具栏

### 2. 灵活的数量设置
- ✅ 每个产品独立设置打印数量
- ✅ 批量设置选中产品的打印数量
- ✅ 支持0-999的数量范围
- ✅ 实时统计总标签数和总打印数

### 3. 字段自定义
- ✅ 可选择标签上显示的字段
- ✅ 支持的字段：产品名称、产品编码、条码、规格、价格、单位
- ✅ 默认选择：产品名称、条码、产品编码

### 4. 模板系统
- ✅ 支持多种标签模板
- ✅ 自动选择默认模板
- ✅ 根据业务类型加载对应模板

### 5. 预览功能
- ✅ 单个产品标签预览
- ✅ 全部选中产品标签预览
- ✅ 实时条码生成
- ✅ 刷新预览功能

### 6. 打印功能
- ✅ 批量打印选中产品标签
- ✅ 支持设置打印份数
- ✅ 条码验证（无条码产品不能打印）
- ✅ 打印前数据验证

## 支持的业务场景

### 1. 采购入库 (PURCHASE_IN)
- 页面：`src/views/erp/purchase/in/index.vue`
- 权限：`erp:purchase-in:print-labels`
- 数据源：采购入库单产品明细

### 2. 其他入库 (STOCK_IN)
- 页面：`src/views/erp/stock/in/index.vue`
- 权限：`erp:stock-in:print-labels`
- 数据源：其他入库单产品明细

## 技术实现

### 核心组件
```
src/components/MultiProductLabelDialog/index.vue
```

### 主要功能模块
1. **左侧设置面板**
   - 模板选择
   - 字段设置
   - 统计信息

2. **右侧操作面板**
   - 产品选择表格
   - 标签预览区域

3. **批量操作工具栏**
   - 全选/取消全选
   - 批量设置数量
   - 清空选择

### 数据流程
```
入库单数据 → 产品列表加载 → 用户选择配置 → 标签生成 → 预览/打印
```

## 使用方法

### 1. 基本操作流程
1. 在入库单列表中点击"打印标签"按钮
2. 在弹出的对话框中选择要打印的产品（勾选复选框）
3. 为每个选中的产品设置打印数量
4. 选择要在标签上显示的字段
5. 预览标签效果
6. 执行打印

### 2. 批量操作
- **全选产品**：点击表格左上角的全选复选框
- **批量设置数量**：选中产品后点击"批量设置数量"按钮
- **清空选择**：点击"清空选择"按钮取消所有选择

### 3. 预览功能
- **单个预览**：点击产品行的"预览"按钮
- **全部预览**：切换到"标签预览"标签页，点击"预览全部"

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    批量标签打印                              │
├─────────────────┬───────────────────────────────────────────┤
│   左侧设置面板   │              右侧操作面板                  │
│                │                                           │
│ ┌─────────────┐ │ ┌─────────────────────────────────────┐   │
│ │  模板设置   │ │ │            产品选择                 │   │
│ └─────────────┘ │ │  ☑ 全选  [批量设置] [清空选择]      │   │
│                │ │                                     │   │
│ ┌─────────────┐ │ │  ☑ 产品A  编码A  条码A  [预览]      │   │
│ │  字段设置   │ │ │  ☐ 产品B  编码B  条码B  [预览]      │   │
│ └─────────────┘ │ │  ☑ 产品C  编码C  条码C  [预览]      │   │
│                │ └─────────────────────────────────────┘   │
│ ┌─────────────┐ │                                           │
│ │  统计信息   │ │ ┌─────────────────────────────────────┐   │
│ │ 产品种类: 2 │ │ │            标签预览                 │   │
│ │ 总标签数: 5 │ │ │  [刷新预览] [预览全部]              │   │
│ │ 总打印数:10 │ │ │                                     │   │
│ └─────────────┘ │ │  [标签1] [标签2] [标签3] ...        │   │
│                │ └─────────────────────────────────────┘   │
└─────────────────┴───────────────────────────────────────────┤
│                    [取消] [打印标签(10)]                     │
└─────────────────────────────────────────────────────────────┘
```

## 数据结构

### 产品信息接口
```typescript
interface ProductLabelInfo {
  productId: number
  productName: string
  productCode: string
  barCode: string
  specification?: string
  price?: number
  unit?: string
  quantity: number        // 入库数量
  inQuantity: number     // 入库数量（显示用）
  printCount: number     // 打印数量
  selected: boolean      // 是否选中
}
```

### 打印配置
```typescript
interface PrintConfig {
  templateId?: number    // 模板ID
  copies: number        // 打印份数
}
```

## 错误处理

### 1. 数据验证
- 入库单无产品信息时显示警告
- 产品无条码时不允许打印
- 未选择产品时提示选择

### 2. 异常处理
- API调用失败时显示错误信息
- 模板加载失败时的降级处理
- 打印失败时的错误提示

## 性能优化

### 1. 组件优化
- 使用 `destroy-on-close` 销毁对话框
- 条码生成采用异步处理
- 大量标签预览时的性能考虑

### 2. 用户体验
- 实时统计信息更新
- 操作反馈和加载状态
- 合理的默认值设置

## 扩展性

### 1. 支持新的业务类型
只需在组件的 `loadProductData` 方法中添加新的类型处理逻辑

### 2. 支持新的字段
在 `availableFields` 配置中添加新字段，并在 `buildLabelData` 方法中处理

### 3. 支持新的模板类型
通过 `loadTemplates` 方法的类型映射添加新模板类型

## 兼容性

- ✅ 保持现有权限控制机制
- ✅ 兼容现有的标签模板系统
- ✅ 兼容现有的条码生成工具
- ✅ 向后兼容原有的打印功能
