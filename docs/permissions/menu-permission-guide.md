# yudao-cloud 系统菜单权限配置指南

## 📋 system_menu 表结构说明

### 表字段详解

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `id` | bigint | 菜单编号（自增主键） | 1001 |
| `name` | varchar(50) | 菜单名称 | "产品标签打印" |
| `permission` | varchar(100) | 权限标识 | "erp:product:print-labels" |
| `type` | tinyint | 菜单类型 | 1=目录, 2=菜单, 3=按钮 |
| `sort` | int | 显示顺序 | 10 |
| `parent_id` | bigint | 父菜单ID | 1000 |
| `path` | varchar(200) | 路由地址 | "/erp/product" |
| `icon` | varchar(100) | 菜单图标 | "ep:goods" |
| `component` | varchar(255) | 组件路径 | "erp/product/index" |
| `component_name` | varchar(255) | 组件名 | "ErpProduct" |
| `status` | tinyint | 状态 | 0=正常, 1=停用 |
| `visible` | bit | 是否可见 | true=可见, false=隐藏 |
| `keep_alive` | bit | 是否缓存 | true=缓存, false=不缓存 |
| `always_show` | bit | 是否总是显示 | true=显示, false=隐藏 |
| `creator` | varchar(64) | 创建者 | "admin" |
| `create_time` | datetime | 创建时间 | 2024-01-01 12:00:00 |
| `updater` | varchar(64) | 更新者 | "admin" |
| `update_time` | datetime | 更新时间 | 2024-01-01 12:00:00 |
| `deleted` | bit | 是否删除 | 0=未删除, 1=已删除 |
| `tenant_id` | bigint | 租户编号 | 0 |

### 菜单类型说明

#### 1. 目录 (type=1)
- **用途**: 用于组织菜单结构，通常不对应具体页面
- **特点**: 有子菜单，显示在侧边栏
- **示例**: "ERP管理"、"系统管理"

#### 2. 菜单 (type=2)
- **用途**: 对应具体的页面路由
- **特点**: 可以点击跳转到具体页面
- **示例**: "产品管理"、"用户管理"

#### 3. 按钮 (type=3)
- **用途**: 页面内的操作按钮权限
- **特点**: 不显示在侧边栏，用于控制页面内按钮的显示
- **示例**: "新增"、"编辑"、"删除"、"打印标签"

## 🔐 权限标识规范

### 命名规则
```
${系统}:${模块}:${操作}
```

### 示例
- `erp:product:query` - ERP系统产品模块查询权限
- `erp:product:create` - ERP系统产品模块新增权限
- `erp:product:update` - ERP系统产品模块修改权限
- `erp:product:delete` - ERP系统产品模块删除权限
- `erp:product:print-labels` - ERP系统产品模块标签打印权限

### 系统前缀
- `system` - 系统管理模块
- `infra` - 基础设施模块
- `erp` - ERP管理模块
- `crm` - CRM管理模块
- `mall` - 商城管理模块

## 📝 标签打印权限配置示例

### 1. 产品标签打印权限

```sql
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status, 
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '产品标签打印',                    -- 权限名称
    'erp:product:print-labels',        -- 权限标识
    3,                                 -- 类型：按钮
    15,                                -- 排序
    (SELECT id FROM system_menu WHERE permission = 'erp:product:query' AND deleted = 0 LIMIT 1), -- 父菜单ID
    '',                                -- 路由地址（按钮类型为空）
    '',                                -- 图标（按钮类型为空）
    '',                                -- 组件路径（按钮类型为空）
    '',                                -- 组件名（按钮类型为空）
    0,                                 -- 状态：正常
    true,                              -- 可见
    true,                              -- 缓存
    true,                              -- 总是显示
    'system',                          -- 创建者
    NOW(),                             -- 创建时间
    'system',                          -- 更新者
    NOW(),                             -- 更新时间
    0,                                 -- 未删除
    0                                  -- 租户ID
);
```

### 2. 角色权限关联

```sql
-- 为超级管理员角色添加权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    1 as role_id,                      -- 角色ID（超级管理员）
    id as menu_id,                     -- 菜单ID
    'system' as creator,               -- 创建者
    NOW() as create_time,              -- 创建时间
    'system' as updater,               -- 更新者
    NOW() as update_time,              -- 更新时间
    0 as deleted,                      -- 未删除
    0 as tenant_id                     -- 租户ID
FROM system_menu 
WHERE permission = 'erp:product:print-labels' AND deleted = 0;
```

## 🔍 权限配置验证

### 1. 查询菜单权限

```sql
-- 查询所有标签打印相关权限
SELECT 
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    status,
    create_time
FROM system_menu 
WHERE permission LIKE '%print-labels%' AND deleted = 0
ORDER BY permission;
```

### 2. 查询角色权限关联

```sql
-- 查询角色权限关联情况
SELECT 
    rm.role_id,
    r.name as role_name,
    m.name as menu_name,
    m.permission,
    rm.create_time
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE m.permission LIKE '%print-labels%' AND rm.deleted = 0
ORDER BY rm.role_id, m.permission;
```

### 3. 查询用户权限

```sql
-- 查询用户是否有标签打印权限
SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.nickname,
    m.permission,
    m.name as permission_name
FROM system_users u
JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE u.deleted = 0 
  AND u.status = 0
  AND m.permission LIKE '%print-labels%'
  AND u.username = 'admin'  -- 替换为具体用户名
ORDER BY u.id, m.permission;
```

## 🛠️ 常用查询语句

### 1. 查询所有角色

```sql
SELECT 
    id,
    name,
    code,
    type,
    status,
    remark
FROM system_role 
WHERE deleted = 0 AND status = 0
ORDER BY type, sort;
```

### 2. 查询ERP模块菜单

```sql
SELECT 
    id,
    name,
    permission,
    type,
    path,
    component,
    parent_id
FROM system_menu 
WHERE permission LIKE 'erp:%' AND deleted = 0
ORDER BY parent_id, sort;
```

### 3. 查询菜单层级结构

```sql
-- 查询ERP产品管理的菜单层级
SELECT 
    m1.id as level1_id,
    m1.name as level1_name,
    m2.id as level2_id,
    m2.name as level2_name,
    m3.id as level3_id,
    m3.name as level3_name,
    m3.permission
FROM system_menu m1
LEFT JOIN system_menu m2 ON m1.id = m2.parent_id AND m2.deleted = 0
LEFT JOIN system_menu m3 ON m2.id = m3.parent_id AND m3.deleted = 0
WHERE m1.permission = 'erp:product:query' AND m1.deleted = 0
ORDER BY m1.sort, m2.sort, m3.sort;
```

## ⚠️ 注意事项

### 1. 权限配置原则
- **最小权限原则**: 只授予必要的权限
- **层级管理**: 按钮权限必须有对应的菜单权限
- **命名规范**: 严格按照命名规范配置权限标识

### 2. 常见问题
- **父菜单ID错误**: 确保父菜单存在且未删除
- **权限标识重复**: 权限标识必须唯一
- **角色ID错误**: 确保角色存在且状态正常

### 3. 最佳实践
- **先查询后配置**: 配置前先查询相关信息
- **批量操作**: 使用事务进行批量权限配置
- **及时验证**: 配置后立即验证权限是否生效

## 📚 相关文档

- [权限配置SQL脚本](../sql/label-print-permissions.sql)
- [功能使用说明](../features/label-printing.md)
- [实施总结](../implementation-summary.md)
