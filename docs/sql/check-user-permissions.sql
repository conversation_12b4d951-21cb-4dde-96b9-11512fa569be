-- 检查用户权限诊断脚本
-- 用于诊断用户是否有条码模板相关权限

-- ========================================
-- 1. 检查当前用户信息（需要替换用户名）
-- ========================================

-- 请将 'admin' 替换为实际的用户名
SET @username = 'admin';

-- 获取用户信息
SELECT 
    '=== 用户基本信息 ===' as info,
    '' as id,
    '' as username,
    '' as nickname,
    '' as status,
    '' as dept_name
UNION ALL
SELECT 
    '',
    CAST(u.id AS CHAR),
    u.username,
    u.nickname,
    CASE u.status 
        WHEN 0 THEN '正常'
        WHEN 1 THEN '停用'
        ELSE '未知'
    END,
    COALESCE(d.name, '无部门')
FROM system_users u
LEFT JOIN system_dept d ON u.dept_id = d.id AND d.deleted = 0
WHERE u.username = @username AND u.deleted = 0;

-- ========================================
-- 2. 检查用户角色信息
-- ========================================

SELECT 
    '=== 用户角色信息 ===' as info,
    '' as role_id,
    '' as role_name,
    '' as role_code,
    '' as role_status
UNION ALL
SELECT 
    '',
    CAST(r.id AS CHAR),
    r.name,
    r.code,
    CASE r.status 
        WHEN 0 THEN '正常'
        WHEN 1 THEN '停用'
        ELSE '未知'
    END
FROM system_users u
JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
JOIN system_role r ON ur.role_id = r.id AND r.deleted = 0
WHERE u.username = @username AND u.deleted = 0
ORDER BY r.id;

-- ========================================
-- 3. 检查条码模板相关权限
-- ========================================

SELECT 
    '=== 条码模板权限检查 ===' as info,
    '' as permission,
    '' as menu_name,
    '' as has_permission
UNION ALL
SELECT 
    '',
    m.permission,
    m.name,
    CASE 
        WHEN rm.id IS NOT NULL THEN '✓ 有权限'
        ELSE '✗ 无权限'
    END
FROM system_menu m
LEFT JOIN (
    SELECT DISTINCT rm.menu_id, rm.id
    FROM system_users u
    JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
    JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
    WHERE u.username = @username AND u.deleted = 0
) rm ON m.id = rm.menu_id
WHERE m.permission LIKE 'erp:barcode-template:%' AND m.deleted = 0
ORDER BY m.permission;

-- ========================================
-- 4. 检查标签打印相关权限
-- ========================================

SELECT 
    '=== 标签打印权限检查 ===' as info,
    '' as permission,
    '' as menu_name,
    '' as has_permission
UNION ALL
SELECT 
    '',
    m.permission,
    m.name,
    CASE 
        WHEN rm.id IS NOT NULL THEN '✓ 有权限'
        ELSE '✗ 无权限'
    END
FROM system_menu m
LEFT JOIN (
    SELECT DISTINCT rm.menu_id, rm.id
    FROM system_users u
    JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
    JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
    WHERE u.username = @username AND u.deleted = 0
) rm ON m.id = rm.menu_id
WHERE m.permission LIKE '%print-labels%' AND m.deleted = 0
ORDER BY m.permission;

-- ========================================
-- 5. 检查ERP相关权限
-- ========================================

SELECT 
    '=== ERP模块权限检查 ===' as info,
    '' as permission,
    '' as menu_name,
    '' as has_permission
UNION ALL
SELECT 
    '',
    m.permission,
    m.name,
    CASE 
        WHEN rm.id IS NOT NULL THEN '✓ 有权限'
        ELSE '✗ 无权限'
    END
FROM system_menu m
LEFT JOIN (
    SELECT DISTINCT rm.menu_id, rm.id
    FROM system_users u
    JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
    JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
    WHERE u.username = @username AND u.deleted = 0
) rm ON m.id = rm.menu_id
WHERE m.permission LIKE 'erp:%' AND m.deleted = 0
ORDER BY m.permission
LIMIT 20;  -- 限制显示数量，避免输出过多

-- ========================================
-- 6. 权限诊断建议
-- ========================================

-- 检查是否有条码模板查询权限
SET @has_template_query = (
    SELECT COUNT(*)
    FROM system_users u
    JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
    JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
    JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
    WHERE u.username = @username 
      AND u.deleted = 0
      AND m.permission = 'erp:barcode-template:query'
);

-- 检查是否有超级管理员角色
SET @is_super_admin = (
    SELECT COUNT(*)
    FROM system_users u
    JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
    JOIN system_role r ON ur.role_id = r.id AND r.deleted = 0
    WHERE u.username = @username 
      AND u.deleted = 0
      AND (r.code = 'super_admin' OR r.name LIKE '%超级管理员%')
);

-- 显示诊断结果和建议
SELECT 
    '=== 权限诊断结果 ===' as diagnosis,
    '' as issue,
    '' as solution
UNION ALL
SELECT 
    '',
    CASE 
        WHEN @has_template_query > 0 THEN '✓ 用户有条码模板查询权限'
        ELSE '✗ 用户缺少条码模板查询权限'
    END,
    CASE 
        WHEN @has_template_query > 0 THEN '权限正常'
        ELSE '需要为用户角色添加 erp:barcode-template:query 权限'
    END
UNION ALL
SELECT 
    '',
    CASE 
        WHEN @is_super_admin > 0 THEN '✓ 用户是超级管理员'
        ELSE '✗ 用户不是超级管理员'
    END,
    CASE 
        WHEN @is_super_admin > 0 THEN '应该有所有权限'
        ELSE '需要手动分配权限或添加到管理员角色'
    END;

-- ========================================
-- 7. 快速修复建议
-- ========================================

-- 获取用户ID和超级管理员角色ID
SET @user_id = (SELECT id FROM system_users WHERE username = @username AND deleted = 0);
SET @admin_role_id = (
    SELECT id FROM system_role 
    WHERE code = 'super_admin' OR name LIKE '%超级管理员%' OR id = 1
    ORDER BY id ASC LIMIT 1
);

SELECT 
    '=== 快速修复建议 ===' as suggestion,
    '' as sql_command
UNION ALL
SELECT 
    '',
    CONCAT('-- 方案1: 将用户添加到超级管理员角色')
UNION ALL
SELECT 
    '',
    CONCAT('INSERT INTO system_user_role (user_id, role_id, creator, create_time, tenant_id) VALUES (', 
           COALESCE(@user_id, 0), ', ', COALESCE(@admin_role_id, 0), ', ''system'', NOW(), 0);')
UNION ALL
SELECT 
    '',
    '-- 方案2: 为用户当前角色添加条码模板权限'
UNION ALL
SELECT 
    '',
    CONCAT('-- 请在管理后台：系统管理 > 角色管理 > 分配权限 中添加条码模板相关权限');

-- 显示需要的权限列表
SELECT 
    '=== 需要的权限列表 ===' as required_permissions,
    permission as permission_code,
    name as permission_name
FROM system_menu 
WHERE permission LIKE 'erp:barcode-template:%' AND deleted = 0
ORDER BY permission;
