-- 扩展现有的条码模板表，添加标签打印相关字段
ALTER TABLE erp_barcode_template 
ADD COLUMN template_config TEXT COMMENT '模板配置JSON(包含元素布局、样式等)',
ADD COLUMN label_width DECIMAL(10,2) DEFAULT 50.0 COMMENT '标签宽度(mm)',
ADD COLUMN label_height DECIMAL(10,2) DEFAULT 30.0 COMMENT '标签高度(mm)',
ADD COLUMN barcode_format VARCHAR(20) DEFAULT 'CODE128' COMMENT '默认条码格式',
ADD COLUMN template_type TINYINT DEFAULT 1 COMMENT '模板类型(1产品标签,2入库标签,3出库标签)',
ADD COLUMN preview_data TEXT COMMENT '预览数据JSON';

-- 创建标签打印任务表
CREATE TABLE erp_label_print_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_no VARCHAR(50) NOT NULL COMMENT '任务编号',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    source_type VARCHAR(20) NOT NULL COMMENT '来源类型(STOCK_IN,STOCK_OUT,PRODUCT,MANUAL)',
    source_id BIGINT COMMENT '来源ID(入库单ID、出库单ID等)',
    print_data JSON COMMENT '打印数据JSON',
    total_count INT DEFAULT 0 COMMENT '总打印数量',
    printed_count INT DEFAULT 0 COMMENT '已打印数量',
    status TINYINT DEFAULT 0 COMMENT '状态(0待打印,1打印中,2已完成,3已取消)',
    print_time DATETIME COMMENT '打印时间',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) DEFAULT b'0' COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    INDEX idx_template_id (template_id),
    INDEX idx_source (source_type, source_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT='ERP 标签打印任务表';

-- 创建标签打印明细表
CREATE TABLE erp_label_print_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    product_name VARCHAR(255) COMMENT '产品名称',
    product_code VARCHAR(100) COMMENT '产品编码',
    barcode_value VARCHAR(100) COMMENT '条码值',
    print_count INT DEFAULT 1 COMMENT '打印数量',
    label_data JSON COMMENT '标签数据JSON',
    status TINYINT DEFAULT 0 COMMENT '状态(0待打印,1已打印)',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) DEFAULT b'0' COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    INDEX idx_task_id (task_id),
    INDEX idx_product_id (product_id),
    INDEX idx_status (status)
) COMMENT='ERP 标签打印明细表';

-- 插入默认标签模板
INSERT INTO erp_barcode_template (
    name, 
    template, 
    template_config,
    label_width,
    label_height,
    barcode_format,
    template_type,
    is_default, 
    status, 
    creator, 
    create_time,
    tenant_id
) VALUES (
    '通用产品标签模板',
    '{"elements":[]}',
    '{
        "elements": [
            {
                "id": "product-name",
                "type": "text",
                "x": 2,
                "y": 2,
                "width": 46,
                "height": 8,
                "fontSize": 14,
                "fontWeight": "bold",
                "textAlign": "center",
                "dataField": "productName",
                "content": "产品名称"
            },
            {
                "id": "product-code",
                "type": "text",
                "x": 2,
                "y": 12,
                "width": 46,
                "height": 6,
                "fontSize": 10,
                "textAlign": "center",
                "dataField": "productCode",
                "content": "产品编码"
            },
            {
                "id": "barcode",
                "type": "barcode",
                "x": 5,
                "y": 20,
                "width": 40,
                "height": 15,
                "dataField": "barCode",
                "barcodeOptions": {
                    "format": "CODE128",
                    "width": 2,
                    "height": 60,
                    "displayValue": true,
                    "fontSize": 10,
                    "textMargin": 2
                }
            },
            {
                "id": "price",
                "type": "text",
                "x": 2,
                "y": 37,
                "width": 23,
                "height": 6,
                "fontSize": 10,
                "dataField": "salePrice",
                "content": "￥0.00"
            },
            {
                "id": "brand",
                "type": "text",
                "x": 25,
                "y": 37,
                "width": 23,
                "height": 6,
                "fontSize": 10,
                "textAlign": "right",
                "dataField": "productBrandName",
                "content": "品牌"
            }
        ]
    }',
    50.0,
    45.0,
    'CODE128',
    1,
    1,
    1,
    'system',
    NOW(),
    0
);

-- 插入入库标签模板
INSERT INTO erp_barcode_template (
    name, 
    template, 
    template_config,
    label_width,
    label_height,
    barcode_format,
    template_type,
    is_default, 
    status, 
    creator, 
    create_time,
    tenant_id
) VALUES (
    '入库标签模板',
    '{"elements":[]}',
    '{
        "elements": [
            {
                "id": "stock-in-no",
                "type": "text",
                "x": 2,
                "y": 2,
                "width": 46,
                "height": 6,
                "fontSize": 12,
                "fontWeight": "bold",
                "textAlign": "center",
                "dataField": "stockInNo",
                "content": "入库单号"
            },
            {
                "id": "product-name",
                "type": "text",
                "x": 2,
                "y": 10,
                "width": 46,
                "height": 8,
                "fontSize": 14,
                "fontWeight": "bold",
                "textAlign": "center",
                "dataField": "productName",
                "content": "产品名称"
            },
            {
                "id": "barcode",
                "type": "barcode",
                "x": 5,
                "y": 20,
                "width": 40,
                "height": 15,
                "dataField": "barCode",
                "barcodeOptions": {
                    "format": "CODE128",
                    "width": 2,
                    "height": 60,
                    "displayValue": true,
                    "fontSize": 10,
                    "textMargin": 2
                }
            },
            {
                "id": "in-time",
                "type": "text",
                "x": 2,
                "y": 37,
                "width": 23,
                "height": 6,
                "fontSize": 9,
                "dataField": "inTime",
                "content": "入库时间"
            },
            {
                "id": "warehouse",
                "type": "text",
                "x": 25,
                "y": 37,
                "width": 23,
                "height": 6,
                "fontSize": 9,
                "textAlign": "right",
                "dataField": "warehouseName",
                "content": "仓库"
            }
        ]
    }',
    50.0,
    45.0,
    'CODE128',
    2,
    1,
    1,
    'system',
    NOW(),
    0
);

-- 为新表添加外键约束
ALTER TABLE erp_label_print_task 
ADD CONSTRAINT fk_print_task_template 
FOREIGN KEY (template_id) REFERENCES erp_barcode_template(id);

ALTER TABLE erp_label_print_detail 
ADD CONSTRAINT fk_print_detail_task 
FOREIGN KEY (task_id) REFERENCES erp_label_print_task(id);

-- 添加索引优化查询性能
CREATE INDEX idx_barcode_template_type ON erp_barcode_template(template_type, status);
CREATE INDEX idx_barcode_template_product ON erp_barcode_template(product_id, is_default);

-- 添加权限数据
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status, 
    creator, create_time, tenant_id
) VALUES 
(
    '标签模板管理', 'erp:barcode-template:query', 2, 1, 
    (SELECT id FROM system_menu WHERE permission = 'erp:product:query' LIMIT 1),
    'barcode-template', 'ep:barcode', 'erp/barcode/template/index', 1,
    'system', NOW(), 0
),
(
    '标签模板新增', 'erp:barcode-template:create', 3, 1,
    (SELECT id FROM system_menu WHERE permission = 'erp:barcode-template:query' LIMIT 1),
    '', '', '', 1, 'system', NOW(), 0
),
(
    '标签模板修改', 'erp:barcode-template:update', 3, 2,
    (SELECT id FROM system_menu WHERE permission = 'erp:barcode-template:query' LIMIT 1),
    '', '', '', 1, 'system', NOW(), 0
),
(
    '标签模板删除', 'erp:barcode-template:delete', 3, 3,
    (SELECT id FROM system_menu WHERE permission = 'erp:barcode-template:query' LIMIT 1),
    '', '', '', 1, 'system', NOW(), 0
),
(
    '标签打印', 'erp:stock-in:print-labels', 3, 10,
    (SELECT id FROM system_menu WHERE permission = 'erp:stock-in:query' LIMIT 1),
    '', '', '', 1, 'system', NOW(), 0
);
