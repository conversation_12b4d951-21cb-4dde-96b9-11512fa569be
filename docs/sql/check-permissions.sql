-- 标签打印功能权限检查脚本
-- 用于验证权限是否正确配置

-- ========================================
-- 1. 检查菜单权限是否存在
-- ========================================
SELECT 
    '菜单权限检查' as check_type,
    CASE 
        WHEN COUNT(*) = 3 THEN '✅ 权限配置完整'
        WHEN COUNT(*) > 0 THEN CONCAT('⚠️ 部分权限缺失，已配置 ', COUNT(*), '/3 个权限')
        ELSE '❌ 权限未配置'
    END as status,
    COUNT(*) as configured_count,
    3 as required_count
FROM system_menu 
WHERE permission IN (
    'erp:product:print-labels',
    'erp:stock-in:print-labels', 
    'erp:stock-out:print-labels'
) AND deleted = 0;

-- ========================================
-- 2. 详细权限列表
-- ========================================
SELECT 
    '详细权限列表' as check_type,
    id,
    name,
    permission,
    type,
    parent_id,
    status,
    CASE 
        WHEN status = 0 THEN '✅ 正常'
        ELSE '❌ 已停用'
    END as status_desc,
    create_time
FROM system_menu 
WHERE permission IN (
    'erp:product:print-labels',
    'erp:stock-in:print-labels', 
    'erp:stock-out:print-labels'
) AND deleted = 0
ORDER BY permission;

-- ========================================
-- 3. 检查父菜单是否存在
-- ========================================
SELECT 
    '父菜单检查' as check_type,
    permission,
    name,
    id,
    CASE 
        WHEN id IS NOT NULL THEN '✅ 存在'
        ELSE '❌ 不存在'
    END as status
FROM system_menu 
WHERE permission IN (
    'erp:product:query',
    'erp:stock-in:query',
    'erp:stock-out:query'
) AND deleted = 0
ORDER BY permission;

-- ========================================
-- 4. 检查角色权限分配
-- ========================================
SELECT 
    '角色权限分配检查' as check_type,
    r.id as role_id,
    r.name as role_name,
    r.code as role_code,
    COUNT(rm.menu_id) as assigned_permissions,
    CASE 
        WHEN COUNT(rm.menu_id) = 3 THEN '✅ 权限完整'
        WHEN COUNT(rm.menu_id) > 0 THEN CONCAT('⚠️ 部分权限，已分配 ', COUNT(rm.menu_id), '/3 个')
        ELSE '❌ 无权限'
    END as status
FROM system_role r
LEFT JOIN system_role_menu rm ON r.id = rm.role_id AND rm.deleted = 0
LEFT JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0 
    AND m.permission IN (
        'erp:product:print-labels',
        'erp:stock-in:print-labels', 
        'erp:stock-out:print-labels'
    )
WHERE r.deleted = 0 AND r.status = 0
GROUP BY r.id, r.name, r.code
ORDER BY r.type, r.sort;

-- ========================================
-- 5. 检查超级管理员权限
-- ========================================
SELECT 
    '超级管理员权限检查' as check_type,
    r.name as role_name,
    m.name as permission_name,
    m.permission,
    CASE 
        WHEN rm.id IS NOT NULL THEN '✅ 已分配'
        ELSE '❌ 未分配'
    END as status
FROM system_role r
CROSS JOIN system_menu m
LEFT JOIN system_role_menu rm ON r.id = rm.role_id AND m.id = rm.menu_id AND rm.deleted = 0
WHERE r.id = 1  -- 假设超级管理员角色ID为1
  AND r.deleted = 0
  AND m.permission IN (
      'erp:product:print-labels',
      'erp:stock-in:print-labels', 
      'erp:stock-out:print-labels'
  )
  AND m.deleted = 0
ORDER BY m.permission;

-- ========================================
-- 6. 检查用户权限（示例：admin用户）
-- ========================================
SELECT 
    '用户权限检查' as check_type,
    u.username,
    u.nickname,
    m.permission,
    m.name as permission_name,
    r.name as role_name,
    CASE 
        WHEN rm.id IS NOT NULL THEN '✅ 有权限'
        ELSE '❌ 无权限'
    END as status
FROM system_users u
CROSS JOIN system_menu m
LEFT JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
LEFT JOIN system_role_menu rm ON ur.role_id = rm.role_id AND m.id = rm.menu_id AND rm.deleted = 0
LEFT JOIN system_role r ON ur.role_id = r.id AND r.deleted = 0
WHERE u.username = 'admin'  -- 可以修改为其他用户名
  AND u.deleted = 0
  AND m.permission IN (
      'erp:product:print-labels',
      'erp:stock-in:print-labels', 
      'erp:stock-out:print-labels'
  )
  AND m.deleted = 0
ORDER BY m.permission;

-- ========================================
-- 7. 权限配置建议
-- ========================================
SELECT 
    '配置建议' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM system_menu WHERE permission LIKE '%print-labels%' AND deleted = 0) = 0 
        THEN '请执行 quick-setup-permissions.sql 脚本配置权限'
        WHEN (SELECT COUNT(*) FROM system_role_menu rm JOIN system_menu m ON rm.menu_id = m.id WHERE m.permission LIKE '%print-labels%' AND rm.deleted = 0 AND m.deleted = 0) = 0
        THEN '权限已创建，但未分配给任何角色，请检查角色权限配置'
        ELSE '权限配置正常，如有问题请检查用户角色分配'
    END as suggestion;

-- ========================================
-- 8. 系统信息
-- ========================================
SELECT 
    '系统信息' as info_type,
    'system_menu' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN permission LIKE 'erp:%' AND deleted = 0 THEN 1 END) as erp_permissions
FROM system_menu

UNION ALL

SELECT 
    '系统信息' as info_type,
    'system_role' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 AND status = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN type = 1 AND deleted = 0 THEN 1 END) as system_roles
FROM system_role

UNION ALL

SELECT 
    '系统信息' as info_type,
    'system_role_menu' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_records,
    COUNT(DISTINCT role_id) as roles_with_permissions
FROM system_role_menu;
