-- 采购入库标签打印权限配置脚本
-- 为采购入库页面添加标签打印功能权限

-- ========================================
-- 1. 添加采购入库标签打印权限
-- ========================================
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '采购入库标签打印',
    'erp:purchase-in:print-labels',
    3,
    15,
    (SELECT id FROM system_menu WHERE permission = 'erp:purchase-in:query' AND deleted = b'0' LIMIT 1),
    '',
    '',
    '',
    '',
    0,
    b'1',
    b'1',
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0'
);

-- ========================================
-- 2. 为超级管理员角色添加权限
-- ========================================
-- 注意：需要根据实际的角色ID进行调整，通常超级管理员角色ID为1
-- 如果不确定角色ID，可以先查询：SELECT id, name, code FROM system_role WHERE type = 1;
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted)
SELECT
    1 as role_id,  -- 超级管理员角色ID，请根据实际情况调整
    id as menu_id,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    b'0' as deleted
FROM system_menu
WHERE permission = 'erp:purchase-in:print-labels' AND deleted = b'0';

-- ========================================
-- 3. 查询验证权限是否添加成功
-- ========================================
SELECT
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    status,
    create_time
FROM system_menu
WHERE permission = 'erp:purchase-in:print-labels' AND deleted = 0;

-- ========================================
-- 4. 查询角色权限关联是否添加成功
-- ========================================
SELECT
    rm.role_id,
    r.name as role_name,
    m.name as menu_name,
    m.permission,
    rm.create_time
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE m.permission = 'erp:purchase-in:print-labels' AND rm.deleted = 0;

-- ========================================
-- 5. 查询采购入库相关的所有权限
-- ========================================
SELECT
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    status
FROM system_menu
WHERE permission LIKE 'erp:purchase-in:%' AND deleted = 0
ORDER BY sort, permission;

-- ========================================
-- 6. 查询当前系统中的角色信息（用于确认角色ID）
-- ========================================
SELECT
    id,
    name,
    code,
    type,
    status,
    remark
FROM system_role
WHERE deleted = 0 AND status = 0
ORDER BY type, sort;

-- ========================================
-- 7. 查询采购入库父菜单信息（用于确认父菜单ID）
-- ========================================
SELECT
    id,
    name,
    permission,
    type,
    path,
    component
FROM system_menu
WHERE permission = 'erp:purchase-in:query' AND deleted = 0;

-- ========================================
-- 8. 检查所有标签打印相关权限
-- ========================================
SELECT
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    status,
    create_time
FROM system_menu
WHERE permission LIKE '%print-labels%' AND deleted = 0
ORDER BY permission;
