-- 修复条码模板权限配置
-- 解决 /admin-api/erp/barcode-template/list?templateType=2 没有操作权限的问题

-- ========================================
-- 1. 检查并添加条码模板相关权限
-- ========================================

-- 查找ERP产品管理的父菜单ID
SET @erp_product_menu_id = (
    SELECT id FROM system_menu 
    WHERE permission = 'erp:product:query' AND deleted = 0 
    LIMIT 1
);

-- 如果找不到ERP产品菜单，则查找ERP模块的根菜单
SET @erp_root_menu_id = (
    SELECT id FROM system_menu 
    WHERE name LIKE '%ERP%' AND type = 1 AND deleted = 0 
    LIMIT 1
);

-- 使用产品菜单ID，如果没有则使用ERP根菜单ID
SET @parent_menu_id = COALESCE(@erp_product_menu_id, @erp_root_menu_id, 0);

-- 检查条码模板查询权限是否存在
SET @template_query_exists = (
    SELECT COUNT(*) FROM system_menu 
    WHERE permission = 'erp:barcode-template:query' AND deleted = 0
);

-- 添加条码模板查询权限（如果不存在）
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    '条码模板查询',
    'erp:barcode-template:query',
    3,  -- 按钮类型
    10,
    @parent_menu_id,
    '',
    '',
    '',
    0,  -- 正常状态
    b'1',
    b'1', 
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0',
    0
WHERE @template_query_exists = 0;

-- 检查条码模板管理菜单是否存在
SET @template_menu_exists = (
    SELECT COUNT(*) FROM system_menu 
    WHERE name = '条码模板管理' AND deleted = 0
);

-- 添加条码模板管理菜单（如果不存在）
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    '条码模板管理',
    'erp:barcode-template:list',
    2,  -- 菜单类型
    15,
    @parent_menu_id,
    'barcode-template',
    'ep:barcode',
    'erp/barcode/template/index',
    0,  -- 正常状态
    b'1',
    b'1', 
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0',
    0
WHERE @template_menu_exists = 0;

-- ========================================
-- 2. 为超级管理员角色添加权限
-- ========================================

-- 获取超级管理员角色ID（通常是1，但也可能不同）
SET @admin_role_id = (
    SELECT id FROM system_role 
    WHERE code = 'super_admin' OR name LIKE '%超级管理员%' OR id = 1
    ORDER BY id ASC
    LIMIT 1
);

-- 获取条码模板查询权限的菜单ID
SET @template_query_menu_id = (
    SELECT id FROM system_menu 
    WHERE permission = 'erp:barcode-template:query' AND deleted = 0
    LIMIT 1
);

-- 为超级管理员添加条码模板查询权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    @admin_role_id,
    @template_query_menu_id,
    'system',
    NOW(),
    'system', 
    NOW(),
    b'0',
    0
WHERE @admin_role_id IS NOT NULL 
  AND @template_query_menu_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM system_role_menu 
      WHERE role_id = @admin_role_id 
        AND menu_id = @template_query_menu_id 
        AND deleted = 0
  );

-- 获取条码模板管理菜单ID
SET @template_menu_menu_id = (
    SELECT id FROM system_menu 
    WHERE name = '条码模板管理' AND deleted = 0
    LIMIT 1
);

-- 为超级管理员添加条码模板管理菜单权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    @admin_role_id,
    @template_menu_menu_id,
    'system',
    NOW(),
    'system', 
    NOW(),
    b'0',
    0
WHERE @admin_role_id IS NOT NULL 
  AND @template_menu_menu_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM system_role_menu 
      WHERE role_id = @admin_role_id 
        AND menu_id = @template_menu_menu_id 
        AND deleted = 0
  );

-- ========================================
-- 3. 添加其他条码模板相关权限
-- ========================================

-- 条码模板新增权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    '条码模板新增',
    'erp:barcode-template:create',
    3,
    11,
    @template_query_menu_id,
    '',
    '',
    '',
    0,
    b'1',
    b'1', 
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0',
    0
WHERE @template_query_menu_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM system_menu 
      WHERE permission = 'erp:barcode-template:create' AND deleted = 0
  );

-- 条码模板修改权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    '条码模板修改',
    'erp:barcode-template:update',
    3,
    12,
    @template_query_menu_id,
    '',
    '',
    '',
    0,
    b'1',
    b'1', 
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0',
    0
WHERE @template_query_menu_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM system_menu 
      WHERE permission = 'erp:barcode-template:update' AND deleted = 0
  );

-- 条码模板删除权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    '条码模板删除',
    'erp:barcode-template:delete',
    3,
    13,
    @template_query_menu_id,
    '',
    '',
    '',
    0,
    b'1',
    b'1', 
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0',
    0
WHERE @template_query_menu_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM system_menu 
      WHERE permission = 'erp:barcode-template:delete' AND deleted = 0
  );

-- ========================================
-- 4. 为超级管理员添加所有条码模板权限
-- ========================================

-- 批量为超级管理员添加所有条码模板相关权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    @admin_role_id,
    m.id,
    'system',
    NOW(),
    'system', 
    NOW(),
    b'0',
    0
FROM system_menu m
WHERE @admin_role_id IS NOT NULL
  AND m.permission LIKE 'erp:barcode-template:%'
  AND m.deleted = 0
  AND NOT EXISTS (
      SELECT 1 FROM system_role_menu rm
      WHERE rm.role_id = @admin_role_id 
        AND rm.menu_id = m.id 
        AND rm.deleted = 0
  );

-- ========================================
-- 5. 验证权限配置
-- ========================================

-- 显示所有条码模板相关权限
SELECT 
    '=== 条码模板权限列表 ===' as info,
    '' as id,
    '' as name,
    '' as permission,
    '' as type,
    '' as parent_id,
    '' as status
UNION ALL
SELECT 
    '',
    CAST(id AS CHAR),
    name,
    permission,
    CASE type 
        WHEN 1 THEN '目录'
        WHEN 2 THEN '菜单' 
        WHEN 3 THEN '按钮'
        ELSE '未知'
    END,
    CAST(parent_id AS CHAR),
    CASE status 
        WHEN 0 THEN '正常'
        WHEN 1 THEN '停用'
        ELSE '未知'
    END
FROM system_menu 
WHERE permission LIKE 'erp:barcode-template:%' AND deleted = 0
ORDER BY permission;

-- 显示超级管理员的条码模板权限
SELECT 
    '=== 超级管理员条码模板权限 ===' as info,
    '' as role_name,
    '' as permission,
    '' as menu_name
UNION ALL
SELECT 
    '',
    r.name,
    m.permission,
    m.name
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE rm.role_id = @admin_role_id
  AND m.permission LIKE 'erp:barcode-template:%'
  AND rm.deleted = 0
ORDER BY m.permission;

-- 显示修复结果
SELECT 
    CONCAT('权限修复完成！超级管理员角色ID: ', COALESCE(@admin_role_id, '未找到')) as result,
    CONCAT('条码模板权限数量: ', COUNT(*)) as permission_count
FROM system_menu 
WHERE permission LIKE 'erp:barcode-template:%' AND deleted = 0;
