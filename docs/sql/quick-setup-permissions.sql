-- 标签打印功能权限快速配置脚本
-- 适用于 yudao-cloud ERP 系统
-- 执行前请确保已备份数据库

-- ========================================
-- 第一步：添加标签打印权限到菜单表
-- ========================================

-- 产品标签打印权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '产品标签打印', 'erp:product:print-labels', 3, 15,
    (SELECT id FROM system_menu WHERE permission = 'erp:product:query' AND deleted = 0 LIMIT 1),
    '', '', '', '', 0, b'1', b'1', b'1', 'system', NOW(), 'system', NOW(), b'0'
);

-- 入库标签打印权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '入库标签打印', 'erp:stock-in:print-labels', 3, 15,
    (SELECT id FROM system_menu WHERE permission = 'erp:stock-in:query' AND deleted = 0 LIMIT 1),
    '', '', '', '', 0, b'1', b'1', b'1', 'system', NOW(), 'system', NOW(), b'0'
);

-- 出库标签打印权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '出库标签打印', 'erp:stock-out:print-labels', 3, 15,
    (SELECT id FROM system_menu WHERE permission = 'erp:stock-out:query' AND deleted = 0 LIMIT 1),
    '', '', '', '', 0, b'1', b'1', b'1', 'system', NOW(), 'system', NOW(), b'0'
);

-- ========================================
-- 第二步：为超级管理员角色分配权限
-- ========================================

-- 为角色ID=1（超级管理员）分配标签打印权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted)
SELECT 1, id, 'system', NOW(), 'system', NOW(), b'0'
FROM system_menu
WHERE permission IN ('erp:product:print-labels', 'erp:stock-in:print-labels', 'erp:stock-out:print-labels')
  AND deleted = b'0';

-- ========================================
-- 第三步：验证配置结果
-- ========================================

-- 查询新增的权限
SELECT '新增权限列表' as info, id, name, permission, parent_id FROM system_menu 
WHERE permission LIKE '%print-labels%' AND deleted = 0;

-- 查询权限分配情况
SELECT '权限分配情况' as info, r.name as role_name, m.name as menu_name, m.permission 
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id 
JOIN system_menu m ON rm.menu_id = m.id 
WHERE m.permission LIKE '%print-labels%' AND rm.deleted = 0 AND r.deleted = 0 AND m.deleted = 0;

-- 提示信息
SELECT '配置完成！' as status, '请重新登录系统以刷新权限缓存' as message;
