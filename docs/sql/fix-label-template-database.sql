-- 修复标签模板数据库结构和数据
-- 执行此脚本前请备份数据库

-- ========================================
-- 1. 检查并添加缺失的字段
-- ========================================

-- 检查 template_type 字段是否存在
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'template_type';

-- 如果字段不存在则添加
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN template_type TINYINT DEFAULT 1 COMMENT ''模板类型(1产品标签,2入库标签,3出库标签)''',
    'SELECT ''template_type字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加其他字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'template_config';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN template_config TEXT COMMENT ''模板配置JSON(包含元素布局、样式等)''',
    'SELECT ''template_config字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'label_width';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN label_width DECIMAL(10,2) DEFAULT 50.0 COMMENT ''标签宽度(mm)''',
    'SELECT ''label_width字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'label_height';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN label_height DECIMAL(10,2) DEFAULT 30.0 COMMENT ''标签高度(mm)''',
    'SELECT ''label_height字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'barcode_format';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN barcode_format VARCHAR(20) DEFAULT ''CODE128'' COMMENT ''默认条码格式''',
    'SELECT ''barcode_format字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND COLUMN_NAME = 'preview_data';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE erp_barcode_template ADD COLUMN preview_data TEXT COMMENT ''预览数据JSON''',
    'SELECT ''preview_data字段已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 2. 更新现有数据
-- ========================================

-- 为现有模板设置默认的模板类型
UPDATE erp_barcode_template 
SET template_type = 1, 
    label_width = 50.0, 
    label_height = 30.0, 
    barcode_format = 'CODE128'
WHERE template_type IS NULL;

-- ========================================
-- 3. 插入默认模板（如果不存在）
-- ========================================

-- 检查是否已有入库标签模板
SET @template_exists = 0;
SELECT COUNT(*) INTO @template_exists 
FROM erp_barcode_template 
WHERE template_type = 2 AND deleted = 0;

-- 如果没有入库标签模板，则插入默认的
INSERT INTO erp_barcode_template (
    name, 
    template, 
    template_config,
    label_width,
    label_height,
    barcode_format,
    template_type,
    is_default, 
    status, 
    creator, 
    create_time,
    tenant_id
)
SELECT 
    '默认入库标签模板',
    '{"elements":[]}',
    '{
        "elements": [
            {
                "id": "product-name",
                "type": "text",
                "x": 2,
                "y": 2,
                "width": 46,
                "height": 8,
                "fontSize": 14,
                "fontWeight": "bold",
                "textAlign": "center",
                "dataField": "productName",
                "content": "产品名称"
            },
            {
                "id": "barcode",
                "type": "barcode",
                "x": 5,
                "y": 15,
                "width": 40,
                "height": 12,
                "dataField": "barCode",
                "barcodeOptions": {
                    "format": "CODE128",
                    "width": 2,
                    "height": 50,
                    "displayValue": true,
                    "fontSize": 10
                }
            }
        ]
    }',
    50.0,
    30.0,
    'CODE128',
    2,
    1,
    1,
    'system',
    NOW(),
    0
WHERE @template_exists = 0;

-- ========================================
-- 4. 添加索引优化查询性能
-- ========================================

-- 检查索引是否存在
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template' 
  AND INDEX_NAME = 'idx_template_type_status';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_template_type_status ON erp_barcode_template(template_type, status)',
    'SELECT ''索引idx_template_type_status已存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 5. 验证修复结果
-- ========================================

-- 检查表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'erp_barcode_template'
ORDER BY ORDINAL_POSITION;

-- 检查模板数据
SELECT 
    id,
    name,
    template_type,
    label_width,
    label_height,
    barcode_format,
    is_default,
    status,
    create_time
FROM erp_barcode_template 
WHERE deleted = 0
ORDER BY template_type, is_default DESC, id;

-- 显示修复完成信息
SELECT '标签模板数据库修复完成！' as message;
SELECT CONCAT('共有 ', COUNT(*), ' 个模板') as template_count 
FROM erp_barcode_template WHERE deleted = 0;
SELECT CONCAT('产品标签模板: ', COUNT(*), ' 个') as product_templates 
FROM erp_barcode_template WHERE template_type = 1 AND deleted = 0;
SELECT CONCAT('入库标签模板: ', COUNT(*), ' 个') as stock_in_templates 
FROM erp_barcode_template WHERE template_type = 2 AND deleted = 0;
SELECT CONCAT('出库标签模板: ', COUNT(*), ' 个') as stock_out_templates 
FROM erp_barcode_template WHERE template_type = 3 AND deleted = 0;
