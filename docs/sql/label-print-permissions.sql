-- 产品标签打印功能权限配置 SQL 脚本
-- 执行此脚本来添加标签打印相关的权限
--
-- system_menu 表结构说明：
-- - id: 菜单编号（自增主键）
-- - name: 菜单名称
-- - permission: 权限标识，格式为 ${系统}:${模块}:${操作}
-- - type: 菜单类型 (1=目录, 2=菜单, 3=按钮)
-- - sort: 显示顺序
-- - parent_id: 父菜单ID
-- - path: 路由地址（仅菜单和目录使用）
-- - icon: 菜单图标（仅菜单和目录使用）
-- - component: 组件路径（仅菜单使用）
-- - component_name: 组件名（仅菜单使用）
-- - status: 状态 (0=正常, 1=停用)
-- - visible: 是否可见 (true=可见, false=隐藏)
-- - keep_alive: 是否缓存 (true=缓存, false=不缓存)
-- - always_show: 是否总是显示 (true=显示, false=隐藏)
-- - creator: 创建者
-- - create_time: 创建时间
-- - updater: 更新者
-- - update_time: 更新时间
-- - deleted: 是否删除 (0=未删除, 1=已删除)
-- - tenant_id: 租户编号

-- 1. 为产品管理添加标签打印权限
-- 注意：这里使用子查询来获取产品管理菜单的ID作为父菜单ID
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '产品标签打印',
    'erp:product:print-labels',
    3,
    15,
    (SELECT id FROM system_menu WHERE permission = 'erp:product:query' AND deleted = b'0' LIMIT 1),
    '',
    '',
    '',
    '',
    0,
    b'1',
    b'1',
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0'
);

-- 2. 为入库管理添加标签打印权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '入库标签打印',
    'erp:stock-in:print-labels',
    3,
    15,
    (SELECT id FROM system_menu WHERE permission = 'erp:stock-in:query' AND deleted = b'0' LIMIT 1),
    '',
    '',
    '',
    '',
    0,
    b'1',
    b'1',
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0'
);

-- 3. 为出库管理添加标签打印权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted
) VALUES (
    '出库标签打印',
    'erp:stock-out:print-labels',
    3,
    15,
    (SELECT id FROM system_menu WHERE permission = 'erp:stock-out:query' AND deleted = b'0' LIMIT 1),
    '',
    '',
    '',
    '',
    0,
    b'1',
    b'1',
    b'1',
    'system',
    NOW(),
    'system',
    NOW(),
    b'0'
);

-- 4. 为超级管理员角色添加这些权限
-- 注意：需要根据实际的角色ID进行调整，通常超级管理员角色ID为1
-- 如果不确定角色ID，可以先查询：SELECT id, name, code FROM system_role WHERE type = 1;
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted)
SELECT
    1 as role_id,  -- 超级管理员角色ID，请根据实际情况调整
    id as menu_id,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    b'0' as deleted
FROM system_menu
WHERE permission IN (
    'erp:product:print-labels',
    'erp:stock-in:print-labels',
    'erp:stock-out:print-labels'
) AND deleted = b'0';

-- 5. 查询验证权限是否添加成功
SELECT
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    status,
    create_time
FROM system_menu
WHERE permission LIKE '%print-labels%' AND deleted = 0
ORDER BY permission;

-- 6. 查询角色权限关联是否添加成功
SELECT
    rm.role_id,
    r.name as role_name,
    m.name as menu_name,
    m.permission,
    rm.create_time
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE m.permission LIKE '%print-labels%' AND rm.deleted = 0
ORDER BY rm.role_id, m.permission;

-- 7. 查询当前系统中的角色信息（用于确认角色ID）
SELECT
    id,
    name,
    code,
    type,
    status,
    remark
FROM system_role
WHERE deleted = 0 AND status = 0
ORDER BY type, sort;

-- 8. 查询ERP相关的父菜单信息（用于确认父菜单ID）
SELECT
    id,
    name,
    permission,
    type,
    path,
    component
FROM system_menu
WHERE permission IN ('erp:product:query', 'erp:stock-in:query', 'erp:stock-out:query')
  AND deleted = 0
ORDER BY permission;
