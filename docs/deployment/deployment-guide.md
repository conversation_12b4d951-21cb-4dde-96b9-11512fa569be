# yudao-cloud ERP 标签打印功能部署指南

## 📋 部署前准备

### 系统要求
- **前端**: Node.js 16+ / pnpm 8+
- **后端**: Java 8+ / MySQL 5.7+
- **浏览器**: Chrome 80+ / Firefox 75+ / Safari 13+ / Edge 80+

### 备份数据
```bash
# 备份数据库（重要！）
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🚀 部署步骤

### 第一步：安装前端依赖

```bash
# 进入前端项目目录
cd yudao-ui/yudao-ui-admin-vue3

# 安装 JsBarcode 依赖
pnpm add jsbarcode
pnpm add -D @types/jsbarcode

# 验证安装
pnpm list jsbarcode
```

### 第二步：配置数据库权限

#### 2.1 快速配置（推荐）
```bash
# 执行快速配置脚本
mysql -u username -p database_name < docs/sql/quick-setup-permissions.sql
```

#### 2.2 详细配置
```bash
# 执行详细配置脚本
mysql -u username -p database_name < docs/sql/label-print-permissions.sql
```

#### 2.3 验证配置
```bash
# 检查权限配置是否正确
mysql -u username -p database_name < docs/sql/check-permissions.sql
```

### 第三步：启动服务

#### 3.1 启动后端服务
```bash
# 进入后端项目目录
cd yudao-server

# 启动服务
./mvnw spring-boot:run
# 或者
java -jar target/yudao-server.jar
```

#### 3.2 启动前端服务
```bash
# 进入前端项目目录
cd yudao-ui/yudao-ui-admin-vue3

# 启动开发服务器
pnpm dev

# 或构建生产版本
pnpm build
```

### 第四步：功能验证

#### 4.1 访问测试页面
- 基础测试：`http://localhost:80/simple-barcode-test.html`
- 功能演示：`http://localhost:80/label-print-demo.html`

#### 4.2 登录系统验证
1. 访问：`http://localhost:80`
2. 登录系统（用户名：admin，密码：admin123）
3. 进入 **ERP管理 > 产品管理 > 产品信息**
4. 点击任意产品的 **"打印标签"** 按钮
5. 验证标签预览和打印功能

## 🔧 配置说明

### 权限配置详解

#### 菜单权限
| 权限标识 | 权限名称 | 类型 | 说明 |
|---------|---------|------|------|
| `erp:product:print-labels` | 产品标签打印 | 按钮 | 产品管理页面的标签打印权限 |
| `erp:stock-in:print-labels` | 入库标签打印 | 按钮 | 入库管理页面的标签打印权限 |
| `erp:stock-out:print-labels` | 出库标签打印 | 按钮 | 出库管理页面的标签打印权限 |

#### 角色分配
- **超级管理员**: 默认拥有所有标签打印权限
- **其他角色**: 需要手动分配相应权限

### 自定义配置

#### 1. 修改标签尺寸
编辑 `src/components/LabelPreviewDialog/index.vue`：
```vue
.label-item {
  width: 60mm;  /* 修改宽度 */
  height: 40mm; /* 修改高度 */
}
```

#### 2. 修改条码格式
编辑 `src/utils/barcode.ts`：
```typescript
const defaultOptions: BarcodeOptions = {
  format: 'CODE128',    // 修改条码格式
  width: 2,             // 修改条码宽度
  height: 100,          // 修改条码高度
  // ... 其他选项
}
```

#### 3. 添加自定义字段
编辑标签模板，添加新的数据字段：
```vue
<div class="product-info">
  <div>编码: {{ item.productCode }}</div>
  <div>规格: {{ item.specification }}</div>
  <div>价格: ¥{{ item.price.toFixed(2) }}</div>
  <!-- 添加新字段 -->
  <div>批次: {{ item.batchNumber }}</div>
</div>
```

## 🔍 故障排除

### 常见问题

#### 1. JsBarcode 库加载失败
**症状**: 控制台报错 "JsBarcode is not defined"
**解决方案**:
```bash
# 重新安装依赖
pnpm remove jsbarcode
pnpm add jsbarcode
pnpm dev
```

#### 2. 权限按钮不显示
**症状**: 页面上看不到"打印标签"按钮
**解决方案**:
```sql
-- 检查权限配置
SELECT * FROM system_menu WHERE permission LIKE '%print-labels%';

-- 检查用户权限
SELECT u.username, m.permission 
FROM system_users u
JOIN system_user_role ur ON u.id = ur.user_id
JOIN system_role_menu rm ON ur.role_id = rm.role_id  
JOIN system_menu m ON rm.menu_id = m.id
WHERE u.username = 'admin' AND m.permission LIKE '%print-labels%';
```

#### 3. 条码生成失败
**症状**: 标签预览中条码显示空白
**解决方案**:
- 检查产品是否有有效的条码
- 验证条码格式是否正确
- 查看浏览器控制台错误信息

#### 4. 打印功能异常
**症状**: 点击打印按钮无反应
**解决方案**:
- 检查浏览器是否允许弹窗
- 确认打印机驱动正常
- 尝试使用不同浏览器

### 调试模式

#### 启用调试日志
在浏览器控制台执行：
```javascript
// 启用详细日志
localStorage.setItem('debug', 'label-print:*')

// 查看权限信息
console.log('用户权限:', JSON.parse(localStorage.getItem('permissions')))
```

#### 检查组件状态
```javascript
// 检查标签预览组件
const labelDialog = document.querySelector('[ref="labelPreviewRef"]')
console.log('标签对话框组件:', labelDialog)
```

## 📊 性能优化

### 前端优化
1. **懒加载**: 标签预览组件按需加载
2. **缓存**: 条码生成结果缓存
3. **批量处理**: 大量标签分批生成

### 后端优化
1. **数据库索引**: 为权限查询添加索引
2. **缓存策略**: 权限信息缓存
3. **API优化**: 减少不必要的数据传输

## 🔒 安全考虑

### 权限控制
- 严格按照最小权限原则分配权限
- 定期审查用户权限分配
- 记录权限变更日志

### 数据安全
- 标签打印不涉及敏感数据传输
- 本地生成条码，无需外部服务
- 打印数据不会存储在服务器

## 📈 监控和维护

### 日志监控
- 监控权限验证失败次数
- 记录标签打印使用频率
- 跟踪错误和异常情况

### 定期维护
- 清理过期的权限缓存
- 更新 JsBarcode 库版本
- 检查浏览器兼容性

## 📞 技术支持

### 获取帮助
- **文档**: 查看 `docs/features/label-printing.md`
- **测试**: 访问 `public/simple-barcode-test.html`
- **权限**: 执行 `docs/sql/check-permissions.sql`

### 联系方式
- 技术问题：检查控制台错误信息
- 权限问题：执行权限检查脚本
- 功能建议：提交功能需求

---

**部署完成后，请重新登录系统以刷新权限缓存！**
