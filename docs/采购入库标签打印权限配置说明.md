# 采购入库标签打印权限配置说明

## 权限信息

### 权限标识符
```
erp:purchase-in:print-labels
```

### 权限详情
- **权限名称**: 采购入库标签打印
- **权限标识**: `erp:purchase-in:print-labels`
- **所属模块**: ERP采购管理
- **权限类型**: 操作权限（type = 3）
- **父菜单**: 采购入库查询权限 (`erp:purchase-in:query`)

## 配置方法

### 方法一：使用SQL脚本（推荐）

执行以下SQL脚本文件：
```bash
docs/sql/purchase-in-label-print-permission.sql
```

### 方法二：手动在管理后台配置

1. **登录管理后台**
   - 使用超级管理员账号登录

2. **进入权限管理**
   - 导航到：系统管理 > 菜单管理

3. **添加权限**
   - 点击"新增"按钮
   - 填写以下信息：
     - 菜单名称：`采购入库标签打印`
     - 权限标识：`erp:purchase-in:print-labels`
     - 菜单类型：`按钮`
     - 父菜单：选择"采购入库"菜单
     - 排序：`15`
     - 状态：`正常`

4. **分配权限给角色**
   - 导航到：系统管理 > 角色管理
   - 选择需要的角色（如超级管理员）
   - 点击"分配权限"
   - 勾选"采购入库标签打印"权限
   - 保存

## 权限验证

### 前端权限验证
在Vue组件中使用 `v-hasPermi` 指令：
```vue
<el-button
  link
  type="success"
  @click="handlePrintLabels(scope.row)"
  v-hasPermi="['erp:purchase-in:print-labels']"
>
  打印标签
</el-button>
```

### 编程式权限检查
```typescript
import { hasPurchaseInLabelPrintPermission } from '@/utils/permission'

// 检查是否有采购入库标签打印权限
if (hasPurchaseInLabelPrintPermission()) {
  // 执行标签打印逻辑
}
```

## 相关权限对比

| 功能模块 | 权限标识符 | 权限名称 |
|---------|-----------|----------|
| 产品管理 | `erp:product:print-labels` | 产品标签打印 |
| 库存入库 | `erp:stock-in:print-labels` | 入库标签打印 |
| 库存出库 | `erp:stock-out:print-labels` | 出库标签打印 |
| **采购入库** | **`erp:purchase-in:print-labels`** | **采购入库标签打印** |

## 权限依赖关系

```
采购入库标签打印权限
├── 依赖：erp:purchase-in:query（采购入库查询权限）
├── 功能：打印采购入库单中产品的标签
└── 位置：采购入库页面操作列
```

## 测试验证

### 1. 权限配置验证
```sql
-- 检查权限是否添加成功
SELECT id, name, permission, type, parent_id, status
FROM system_menu 
WHERE permission = 'erp:purchase-in:print-labels' AND deleted = 0;
```

### 2. 角色权限验证
```sql
-- 检查角色是否有该权限
SELECT rm.role_id, r.name as role_name, m.name as menu_name, m.permission
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE m.permission = 'erp:purchase-in:print-labels' AND rm.deleted = 0;
```

### 3. 功能测试
1. 使用有权限的账号登录
2. 进入采购入库页面
3. 确认操作列显示"打印标签"按钮
4. 点击按钮测试功能是否正常

### 4. 权限控制测试
1. 使用没有权限的账号登录
2. 进入采购入库页面
3. 确认操作列不显示"打印标签"按钮

## 注意事项

1. **权限层级**: 该权限是操作权限，需要在对应的父菜单下创建
2. **角色分配**: 需要为相应的角色分配该权限才能使用
3. **前端缓存**: 权限变更后可能需要清除浏览器缓存或重新登录
4. **后端接口**: 如果有对应的后端接口，也需要添加相应的权限验证

## 故障排除

### 问题1: 按钮不显示
- 检查用户是否有该权限
- 检查权限标识符是否正确
- 清除浏览器缓存重新登录

### 问题2: 权限添加失败
- 检查父菜单ID是否正确
- 检查权限标识符是否重复
- 检查数据库连接是否正常

### 问题3: 角色权限分配失败
- 检查角色ID是否正确
- 检查菜单ID是否存在
- 检查是否有重复的权限分配
