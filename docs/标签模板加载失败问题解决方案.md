# 标签模板加载失败问题解决方案

## 问题描述

在使用多产品标签打印功能时，出现"加载打印模板失败"的错误。经过分析发现是后端接口不完善导致的。

## 问题原因

1. **数据库表结构不完整**
   - `erp_barcode_template` 表缺少 `template_type` 字段
   - 缺少其他标签打印相关字段

2. **后端接口缺失**
   - 缺少按模板类型筛选的接口
   - 前端调用的 `getTemplatesByType` 方法不存在

3. **前端API调用错误**
   - 调用了不存在的接口方法

## 解决方案

### 1. 数据库修复

执行数据库修复脚本：
```sql
-- 执行修复脚本
source docs/sql/fix-label-template-database.sql
```

或者手动执行以下SQL：
```sql
-- 添加模板类型字段
ALTER TABLE erp_barcode_template 
ADD COLUMN template_type TINYINT DEFAULT 1 COMMENT '模板类型(1产品标签,2入库标签,3出库标签)';

-- 添加其他必要字段
ALTER TABLE erp_barcode_template 
ADD COLUMN template_config TEXT COMMENT '模板配置JSON',
ADD COLUMN label_width DECIMAL(10,2) DEFAULT 50.0 COMMENT '标签宽度(mm)',
ADD COLUMN label_height DECIMAL(10,2) DEFAULT 30.0 COMMENT '标签高度(mm)',
ADD COLUMN barcode_format VARCHAR(20) DEFAULT 'CODE128' COMMENT '默认条码格式',
ADD COLUMN preview_data TEXT COMMENT '预览数据JSON';

-- 创建索引
CREATE INDEX idx_template_type_status ON erp_barcode_template(template_type, status);
```

### 2. 后端代码更新

#### 2.1 更新实体类
在 `BarcodeTemplateDO.java` 中添加新字段：
```java
/**
 * 模板类型(1产品标签,2入库标签,3出库标签)
 */
private Integer templateType;

/**
 * 模板配置JSON
 */
private String templateConfig;

// ... 其他字段
```

#### 2.2 更新Mapper
在 `BarcodeTemplateMapper.java` 中添加按类型查询方法：
```java
default List<BarcodeTemplateDO> selectByTemplateType(Integer templateType) {
    return selectList(new LambdaQueryWrapperX<BarcodeTemplateDO>()
            .eqIfPresent(BarcodeTemplateDO::getTemplateType, templateType)
            .eq(BarcodeTemplateDO::getStatus, 1)
            .orderByDesc(BarcodeTemplateDO::getIsDefault)
            .orderByDesc(BarcodeTemplateDO::getId));
}
```

#### 2.3 更新Service
在 `BarcodeTemplateService.java` 中添加接口方法：
```java
/**
 * 根据模板类型获得条码模板列表
 */
List<BarcodeTemplateDO> getBarcodeTemplatesByType(Integer templateType);
```

在 `BarcodeTemplateServiceImpl.java` 中实现：
```java
@Override
public List<BarcodeTemplateDO> getBarcodeTemplatesByType(Integer templateType) {
    return barcodeTemplateMapper.selectByTemplateType(templateType);
}
```

#### 2.4 更新Controller
在 `BarcodeTemplateController.java` 中添加接口：
```java
@GetMapping("/list")
@Operation(summary = "获得条码模板列表")
@PreAuthorize("@ss.hasPermission('erp:barcode-template:query')")
public CommonResult<List<BarcodeTemplateRespVO>> getBarcodeTemplateList(
        @RequestParam(value = "templateType", required = false) Integer templateType) {
    List<BarcodeTemplateDO> list;
    if (templateType != null) {
        list = barcodeTemplateService.getBarcodeTemplatesByType(templateType);
    } else {
        list = barcodeTemplateService.getBarcodeTemplateSimpleList();
    }
    return success(BeanUtils.toBean(list, BarcodeTemplateRespVO.class));
}
```

### 3. 前端代码修复

修复 `MultiProductLabelDialog/index.vue` 中的API调用：
```typescript
/** 加载模板列表 */
const loadTemplates = async (type: string) => {
  try {
    const templateType = type === 'STOCK_IN' ? 2 : type === 'STOCK_OUT' ? 3 : type === 'PURCHASE_IN' ? 2 : 1
    // 修复：使用正确的API方法
    const data = await LabelTemplateApi.getLabelTemplateList({ templateType })
    templateList.value = data
    
    // 自动选择默认模板
    const defaultTemplate = data.find(t => t.isDefault)
    if (defaultTemplate) {
      printConfig.templateId = defaultTemplate.id
      await handleTemplateChange()
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  }
}
```

## 验证修复

### 1. 数据库验证
```sql
-- 检查表结构
DESCRIBE erp_barcode_template;

-- 检查模板数据
SELECT id, name, template_type, is_default, status 
FROM erp_barcode_template 
WHERE deleted = 0;
```

### 2. 接口验证
```bash
# 测试获取入库标签模板
curl -X GET "http://localhost:8080/admin-api/erp/barcode-template/list?templateType=2" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 功能验证
1. 进入采购入库页面
2. 点击"打印标签"按钮
3. 检查是否能正常加载模板列表
4. 验证模板选择和预览功能

## 模板类型说明

| 类型值 | 说明 | 使用场景 |
|--------|------|----------|
| 1 | 产品标签 | 产品管理页面的标签打印 |
| 2 | 入库标签 | 采购入库、其他入库的标签打印 |
| 3 | 出库标签 | 销售出库、其他出库的标签打印 |

## 注意事项

1. **数据备份**：执行数据库修改前请备份数据
2. **权限检查**：确保用户有相应的模板查询权限
3. **缓存清理**：修改后可能需要重启应用或清理缓存
4. **测试验证**：在生产环境部署前请在测试环境充分验证

## 常见问题

### Q1: 执行SQL时提示字段已存在
A: 这是正常的，说明字段已经添加过了，可以忽略该错误。

### Q2: 模板列表为空
A: 检查数据库中是否有对应类型的模板数据，可以手动插入默认模板。

### Q3: 权限错误
A: 确保用户角色有 `erp:barcode-template:query` 权限。

### Q4: 前端仍然报错
A: 清除浏览器缓存，重新构建前端项目。

## 后续优化建议

1. **模板管理界面**：添加模板类型的管理功能
2. **默认模板**：为每种类型设置默认模板
3. **模板验证**：添加模板配置的有效性验证
4. **批量导入**：支持模板的批量导入导出功能
