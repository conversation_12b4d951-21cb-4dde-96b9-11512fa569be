# 产品标签打印功能使用说明

## 功能概述

基于 JsBarcode 库实现的产品标签打印功能，支持在产品管理和入库管理页面直接打印产品标签。

## 功能特性

- ✅ 支持多种条码格式（CODE128、EAN-13、CODE39等）
- ✅ 实时标签预览
- ✅ 批量标签打印
- ✅ 自定义标签数量
- ✅ 响应式标签设计
- ✅ 浏览器原生打印支持

## 使用方法

### 1. 产品管理页面

1. 进入 **ERP管理 > 产品管理 > 产品信息**
2. 在产品列表中找到需要打印标签的产品
3. 点击操作列中的 **"打印标签"** 按钮
4. 在弹出的标签预览对话框中：
   - 查看标签预览效果
   - 调整打印数量（1-100张）
   - 点击 **"打印"** 按钮完成打印

### 2. 入库管理页面

1. 进入 **ERP管理 > 库存管理 > 其它入库**
2. 在入库单列表中找到需要打印标签的入库单
3. 点击操作列中的 **"打印标签"** 按钮
4. 系统会自动获取入库单中第一个产品的信息生成标签
5. 在标签预览对话框中完成打印操作

## 标签内容

每个标签包含以下信息：
- **产品名称**：显示在标签顶部，加粗居中
- **条码**：CODE128格式条码，包含条码值显示
- **产品编码**：产品的唯一编码
- **规格信息**：产品规格（如果有）
- **价格信息**：产品价格（如果有）

## 技术实现

### 核心技术栈
- **JsBarcode 3.11.5**：条码生成库
- **Vue 3 + TypeScript**：前端框架
- **Element Plus**：UI组件库
- **Canvas API**：条码渲染

### 核心文件
```
src/
├── utils/barcode.ts                    # 条码生成工具类
├── components/LabelPreviewDialog/      # 标签预览对话框组件
│   └── index.vue
├── views/erp/product/product/          # 产品管理页面
│   └── index.vue
└── views/erp/stock/in/                 # 入库管理页面
    └── index.vue
```

### 权限配置
- `erp:product:print-labels` - 产品标签打印权限
- `erp:stock-in:print-labels` - 入库标签打印权限

## 安装部署

### 1. 依赖安装
```bash
# 安装 JsBarcode 库
pnpm add jsbarcode

# 安装类型定义（开发依赖）
pnpm add -D @types/jsbarcode
```

### 2. 权限配置
执行 SQL 脚本添加权限：
```bash
# 执行权限配置脚本
mysql -u username -p database_name < docs/sql/label-print-permissions.sql
```

### 3. 功能验证
访问测试页面验证功能：
```
http://localhost:80/simple-barcode-test.html
```

## 自定义配置

### 1. 修改标签尺寸
在 `LabelPreviewDialog/index.vue` 中修改：
```vue
.label-preview {
  width: 60mm;  /* 标签宽度 */
  height: 40mm; /* 标签高度 */
}
```

### 2. 修改条码选项
在 `generateBarcodes()` 函数中修改：
```typescript
const options: BarcodeOptions = {
  format: 'CODE128',    // 条码格式
  width: 2,             // 条码宽度
  height: 60,           // 条码高度
  displayValue: true,   // 显示条码值
  fontSize: 12,         // 字体大小
  // ... 其他选项
}
```

### 3. 添加新的标签字段
在标签模板中添加新字段：
```vue
<div class="product-info">
  <div>编码: {{ item.productCode }}</div>
  <div>规格: {{ item.specification }}</div>
  <div>价格: ¥{{ item.price.toFixed(2) }}</div>
  <!-- 添加新字段 -->
  <div>批次: {{ item.batchNumber }}</div>
</div>
```

## 故障排除

### 1. 条码生成失败
- 检查条码内容是否符合格式要求
- 确认 JsBarcode 库是否正确加载
- 查看浏览器控制台错误信息

### 2. 打印功能异常
- 确认浏览器允许弹窗
- 检查打印机驱动是否正常
- 尝试使用不同浏览器

### 3. 权限问题
- 确认用户角色包含标签打印权限
- 检查权限配置是否正确执行
- 重新登录刷新权限缓存

## 扩展功能

### 1. 支持更多页面
可以在其他页面（如出库管理、库存盘点等）添加标签打印功能：

```vue
<!-- 在操作列添加按钮 -->
<el-button
  link
  type="success"
  @click="handlePrintLabels(scope.row)"
  v-hasPermi="['erp:your-module:print-labels']"
>
  打印标签
</el-button>

<!-- 引入组件 -->
<LabelPreviewDialog ref="labelPreviewRef" />
```

### 2. 自定义标签模板
可以扩展支持多种标签模板，让用户选择不同的标签样式。

### 3. 批量打印优化
可以支持选择多个产品进行批量标签打印。

## 更新日志

### v1.0.0 (2025-07-23)
- ✅ 初始版本发布
- ✅ 支持产品管理页面标签打印
- ✅ 支持入库管理页面标签打印
- ✅ 集成 JsBarcode 条码生成
- ✅ 实现标签预览和打印功能
