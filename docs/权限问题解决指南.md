# 权限问题解决指南

## 问题描述

访问 `/admin-api/erp/barcode-template/list?templateType=2` 时提示"没有操作权限"。

## 问题原因

用户缺少 `erp:barcode-template:query` 权限，无法访问条码模板查询接口。

## 解决方案

### 方案一：自动修复（推荐）

执行权限修复脚本：

```bash
# 1. 修复权限配置
mysql -u username -p database_name < docs/sql/fix-barcode-template-permissions.sql

# 2. 检查权限是否生效
mysql -u username -p database_name < docs/sql/check-user-permissions.sql
```

### 方案二：手动配置

#### 1. 在管理后台配置权限

1. **登录管理后台**
   - 使用超级管理员账号登录

2. **添加权限菜单**
   - 导航到：系统管理 > 菜单管理
   - 点击"新增"按钮
   - 填写以下信息：
     ```
     菜单名称：条码模板查询
     权限标识：erp:barcode-template:query
     菜单类型：按钮
     父菜单：选择ERP相关菜单
     状态：正常
     ```

3. **分配权限给角色**
   - 导航到：系统管理 > 角色管理
   - 选择用户所属的角色
   - 点击"分配权限"
   - 勾选"条码模板查询"权限
   - 保存

#### 2. 直接SQL配置

```sql
-- 1. 添加条码模板查询权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id, status,
    creator, create_time, tenant_id
) VALUES (
    '条码模板查询',
    'erp:barcode-template:query',
    3,  -- 按钮类型
    10,
    (SELECT id FROM system_menu WHERE permission = 'erp:product:query' LIMIT 1),
    0,  -- 正常状态
    'system',
    NOW(),
    0
);

-- 2. 为超级管理员角色添加权限
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, tenant_id)
SELECT 
    1,  -- 超级管理员角色ID，请根据实际情况调整
    id,
    'system',
    NOW(),
    0
FROM system_menu 
WHERE permission = 'erp:barcode-template:query' AND deleted = 0;
```

### 方案三：临时解决（开发环境）

如果是开发环境，可以临时关闭权限验证：

1. **修改Controller注解**
   ```java
   // 临时注释掉权限验证
   // @PreAuthorize("@ss.hasPermission('erp:barcode-template:query')")
   public CommonResult<List<BarcodeTemplateRespVO>> getBarcodeTemplateList(...)
   ```

2. **配置文件关闭权限**
   ```yaml
   # application-dev.yml
   yudao:
     security:
       permit-all: true  # 开发环境关闭权限验证
   ```

## 验证修复

### 1. 检查权限是否添加成功

```sql
-- 检查权限菜单
SELECT id, name, permission, type, status 
FROM system_menu 
WHERE permission = 'erp:barcode-template:query' AND deleted = 0;

-- 检查角色权限关联
SELECT rm.role_id, r.name as role_name, m.permission
FROM system_role_menu rm
JOIN system_role r ON rm.role_id = r.id AND r.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE m.permission = 'erp:barcode-template:query' AND rm.deleted = 0;
```

### 2. 检查用户权限

```sql
-- 检查用户是否有权限（替换 'admin' 为实际用户名）
SELECT DISTINCT u.username, m.permission, m.name
FROM system_users u
JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
JOIN system_role_menu rm ON ur.role_id = rm.role_id AND rm.deleted = 0
JOIN system_menu m ON rm.menu_id = m.id AND m.deleted = 0
WHERE u.username = 'admin' 
  AND u.deleted = 0
  AND m.permission = 'erp:barcode-template:query';
```

### 3. 测试接口访问

```bash
# 测试接口是否可以正常访问
curl -X GET "http://localhost:8080/admin-api/erp/barcode-template/list?templateType=2" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 4. 前端功能测试

1. 清除浏览器缓存
2. 重新登录系统
3. 进入采购入库页面
4. 点击"打印标签"按钮
5. 检查是否能正常加载模板列表

## 权限体系说明

### 条码模板相关权限

| 权限标识 | 权限名称 | 说明 |
|---------|----------|------|
| `erp:barcode-template:query` | 条码模板查询 | 查看模板列表、获取模板详情 |
| `erp:barcode-template:create` | 条码模板新增 | 创建新的模板 |
| `erp:barcode-template:update` | 条码模板修改 | 编辑现有模板 |
| `erp:barcode-template:delete` | 条码模板删除 | 删除模板 |

### 标签打印相关权限

| 权限标识 | 权限名称 | 说明 |
|---------|----------|------|
| `erp:purchase-in:print-labels` | 采购入库标签打印 | 采购入库页面的标签打印功能 |
| `erp:stock-in:print-labels` | 其他入库标签打印 | 其他入库页面的标签打印功能 |
| `erp:stock-out:print-labels` | 出库标签打印 | 出库页面的标签打印功能 |

## 常见问题

### Q1: 权限添加后仍然提示无权限
**A**: 清除用户缓存或重新登录，权限变更需要重新获取用户信息。

### Q2: 找不到父菜单ID
**A**: 检查ERP模块菜单是否存在，或者将parent_id设置为0（根菜单）。

### Q3: 超级管理员也没有权限
**A**: 检查超级管理员角色是否正确配置，确认角色状态为正常。

### Q4: 接口返回404
**A**: 检查后端代码是否正确部署，确认接口路径是否正确。

## 注意事项

1. **权限缓存**：权限变更后可能需要清除缓存或重启应用
2. **租户隔离**：多租户环境下注意tenant_id的设置
3. **角色继承**：确保用户角色的权限继承关系正确
4. **接口版本**：确认前后端接口版本一致

## 后续优化建议

1. **权限预设**：在系统初始化时预设所有必要权限
2. **权限检查**：添加权限一致性检查工具
3. **文档完善**：维护完整的权限清单文档
4. **自动化测试**：添加权限相关的自动化测试用例
