<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单条码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .controls {
            margin: 20px 0;
        }
        .controls input, .controls button {
            margin: 5px;
            padding: 8px;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
        .label-preview {
            width: 60mm;
            height: 40mm;
            border: 1px solid #ddd;
            margin: 10px;
            padding: 5mm;
            background: white;
            display: inline-block;
            vertical-align: top;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .product-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }
        .barcode-container {
            text-align: center;
            margin: 5px 0;
        }
        .product-info {
            font-size: 10px;
            line-height: 1.2;
        }
    </style>
</head>
<body>
    <h1>JsBarcode 简单测试</h1>
    
    <div class="test-section">
        <h2>库加载状态</h2>
        <div id="library-status">检查中...</div>
    </div>
    
    <div class="test-section">
        <h2>基础条码生成</h2>
        <div class="controls">
            <input type="text" id="barcode-input" value="1234567890128" placeholder="输入条码">
            <button onclick="testBasicBarcode()">生成条码</button>
        </div>
        <div>
            <canvas id="basic-barcode"></canvas>
        </div>
        <div id="basic-status"></div>
    </div>
    
    <div class="test-section">
        <h2>产品标签测试</h2>
        <div class="controls">
            <input type="text" id="product-name" value="测试产品" placeholder="产品名称">
            <input type="text" id="product-barcode" value="1234567890128" placeholder="产品条码">
            <button onclick="testProductLabel()">生成标签</button>
        </div>
        <div id="label-container"></div>
        <div id="label-status"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        function checkLibraryStatus() {
            const statusDiv = document.getElementById('library-status');
            if (typeof JsBarcode !== 'undefined') {
                statusDiv.innerHTML = '<div class="success">JsBarcode 库加载成功</div>';
                return true;
            } else {
                statusDiv.innerHTML = '<div class="error">JsBarcode 库加载失败</div>';
                return false;
            }
        }
        
        function testBasicBarcode() {
            const statusDiv = document.getElementById('basic-status');
            const canvas = document.getElementById('basic-barcode');
            const input = document.getElementById('barcode-input');
            
            try {
                JsBarcode(canvas, input.value, {
                    format: 'CODE128',
                    width: 2,
                    height: 100,
                    displayValue: true,
                    fontSize: 14,
                    textMargin: 2,
                    background: '#ffffff',
                    lineColor: '#000000'
                });
                statusDiv.innerHTML = '<div class="success">条码生成成功</div>';
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">条码生成失败: ' + error.message + '</div>';
            }
        }
        
        function testProductLabel() {
            const statusDiv = document.getElementById('label-status');
            const container = document.getElementById('label-container');
            
            try {
                const productName = document.getElementById('product-name').value;
                const productBarcode = document.getElementById('product-barcode').value;
                
                container.innerHTML = '<div class="label-preview"><div class="product-name">' + productName + '</div><div class="barcode-container"><canvas class="product-barcode-canvas"></canvas></div><div class="product-info"><div>编码: TEST001</div><div>价格: ¥99.99</div></div></div>';
                
                const canvas = container.querySelector('.product-barcode-canvas');
                JsBarcode(canvas, productBarcode, {
                    format: 'CODE128',
                    width: 1.5,
                    height: 50,
                    displayValue: true,
                    fontSize: 10,
                    textMargin: 1,
                    background: '#ffffff',
                    lineColor: '#000000'
                });
                
                statusDiv.innerHTML = '<div class="success">产品标签生成成功</div>';
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">产品标签生成失败: ' + error.message + '</div>';
            }
        }
        
        window.onload = function() {
            setTimeout(checkLibraryStatus, 1000);
        };
    </script>
</body>
</html>
