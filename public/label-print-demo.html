<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品标签打印功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
        }
        
        .form-item label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #555;
        }
        
        .form-item input, .form-item select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-item input:focus, .form-item select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .preview-area {
            background: white;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .label-item {
            width: 60mm;
            height: 40mm;
            border: 1px solid #ddd;
            background: white;
            padding: 3mm;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .barcode-container {
            text-align: center;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .barcode-canvas {
            max-width: 100%;
            max-height: 100%;
        }
        
        .product-info {
            font-size: 10px;
            line-height: 1.3;
        }
        
        .product-info div {
            margin: 1px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        
        @media print {
            body * {
                visibility: hidden;
            }
            .print-area, .print-area * {
                visibility: visible;
            }
            .print-area {
                position: absolute;
                left: 0;
                top: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ 产品标签打印功能演示</h1>
            <p>基于 JsBarcode 的智能标签打印解决方案</p>
        </div>
        
        <div class="content">
            <!-- 功能状态检查 -->
            <div class="demo-section">
                <h2>📊 系统状态检查</h2>
                <div id="system-status">
                    <div class="status info">正在检查系统状态...</div>
                </div>
            </div>
            
            <!-- 产品信息配置 -->
            <div class="demo-section">
                <h2>📝 产品信息配置</h2>
                <div class="form-group">
                    <div class="form-item">
                        <label for="product-name">产品名称</label>
                        <input type="text" id="product-name" value="熊猫机车配件 - 高性能刹车片" placeholder="请输入产品名称">
                    </div>
                    <div class="form-item">
                        <label for="product-code">产品编码</label>
                        <input type="text" id="product-code" value="PD-BRK-001" placeholder="请输入产品编码">
                    </div>
                    <div class="form-item">
                        <label for="product-barcode">产品条码</label>
                        <input type="text" id="product-barcode" value="1234567890128" placeholder="请输入产品条码">
                    </div>
                    <div class="form-item">
                        <label for="product-spec">产品规格</label>
                        <input type="text" id="product-spec" value="前轮专用 / 陶瓷材质" placeholder="请输入产品规格">
                    </div>
                    <div class="form-item">
                        <label for="product-price">产品价格</label>
                        <input type="number" id="product-price" value="299.99" step="0.01" placeholder="请输入产品价格">
                    </div>
                    <div class="form-item">
                        <label for="product-unit">产品单位</label>
                        <select id="product-unit">
                            <option value="套">套</option>
                            <option value="个">个</option>
                            <option value="对">对</option>
                            <option value="组">组</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 标签配置 -->
            <div class="demo-section">
                <h2>⚙️ 标签配置</h2>
                <div class="form-group">
                    <div class="form-item">
                        <label for="label-quantity">打印数量</label>
                        <input type="number" id="label-quantity" value="3" min="1" max="20" placeholder="请输入打印数量">
                    </div>
                    <div class="form-item">
                        <label for="barcode-format">条码格式</label>
                        <select id="barcode-format">
                            <option value="CODE128">CODE128</option>
                            <option value="EAN13">EAN-13</option>
                            <option value="CODE39">CODE39</option>
                            <option value="ITF14">ITF-14</option>
                        </select>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="generateLabels()">🎨 生成标签预览</button>
                    <button class="btn btn-success" onclick="printLabels()">🖨️ 打印标签</button>
                </div>
                <div id="label-status"></div>
            </div>
            
            <!-- 标签预览 -->
            <div class="demo-section">
                <h2>👀 标签预览</h2>
                <div class="preview-area" id="preview-area">
                    <div style="color: #999; font-size: 18px;">
                        点击"生成标签预览"按钮查看效果
                    </div>
                </div>
            </div>
            
            <!-- 功能特性 -->
            <div class="demo-section">
                <h2>✨ 功能特性</h2>
                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <div class="feature-title">响应式设计</div>
                        <div class="feature-desc">支持各种设备和屏幕尺寸，标签自动适配</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <div class="feature-title">多种条码格式</div>
                        <div class="feature-desc">支持 CODE128、EAN-13、CODE39 等多种条码格式</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">实时预览</div>
                        <div class="feature-desc">所见即所得的标签预览，支持实时编辑</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🖨️</div>
                        <div class="feature-title">批量打印</div>
                        <div class="feature-desc">支持批量生成和打印多个标签</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">高性能</div>
                        <div class="feature-desc">基于 Canvas 的高性能条码生成</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <div class="feature-title">权限控制</div>
                        <div class="feature-desc">完善的权限管理，确保操作安全</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        // 系统状态检查
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let statusHtml = '';
            
            // 检查 JsBarcode 库
            if (typeof JsBarcode !== 'undefined') {
                statusHtml += '<div class="status success">✅ JsBarcode 库加载成功</div>';
            } else {
                statusHtml += '<div class="status error">❌ JsBarcode 库加载失败</div>';
            }
            
            // 检查 Canvas 支持
            const canvas = document.createElement('canvas');
            if (canvas.getContext && canvas.getContext('2d')) {
                statusHtml += '<div class="status success">✅ Canvas 支持正常</div>';
            } else {
                statusHtml += '<div class="status error">❌ Canvas 不支持</div>';
            }
            
            // 检查打印支持
            if (window.print) {
                statusHtml += '<div class="status success">✅ 打印功能支持正常</div>';
            } else {
                statusHtml += '<div class="status error">❌ 打印功能不支持</div>';
            }
            
            statusDiv.innerHTML = statusHtml;
        }
        
        // 生成标签
        function generateLabels() {
            const statusDiv = document.getElementById('label-status');
            const previewArea = document.getElementById('preview-area');
            
            try {
                // 获取表单数据
                const productName = document.getElementById('product-name').value;
                const productCode = document.getElementById('product-code').value;
                const productBarcode = document.getElementById('product-barcode').value;
                const productSpec = document.getElementById('product-spec').value;
                const productPrice = parseFloat(document.getElementById('product-price').value);
                const productUnit = document.getElementById('product-unit').value;
                const quantity = parseInt(document.getElementById('label-quantity').value);
                const barcodeFormat = document.getElementById('barcode-format').value;
                
                // 验证必填字段
                if (!productName || !productBarcode) {
                    throw new Error('产品名称和条码不能为空');
                }
                
                // 清空预览区域
                previewArea.innerHTML = '';
                
                // 生成标签
                for (let i = 0; i < quantity; i++) {
                    const labelDiv = document.createElement('div');
                    labelDiv.className = 'label-item';
                    labelDiv.innerHTML = `
                        <div class="product-name">${productName}</div>
                        <div class="barcode-container">
                            <canvas class="barcode-canvas" id="barcode-${i}"></canvas>
                        </div>
                        <div class="product-info">
                            <div>编码: ${productCode}</div>
                            <div>规格: ${productSpec}</div>
                            <div>价格: ¥${productPrice.toFixed(2)}/${productUnit}</div>
                            <div>序号: ${i + 1}/${quantity}</div>
                        </div>
                    `;
                    previewArea.appendChild(labelDiv);
                    
                    // 生成条码
                    setTimeout(() => {
                        const canvas = document.getElementById(`barcode-${i}`);
                        if (canvas) {
                            JsBarcode(canvas, productBarcode, {
                                format: barcodeFormat,
                                width: 1.5,
                                height: 50,
                                displayValue: true,
                                fontSize: 10,
                                textMargin: 1,
                                background: '#ffffff',
                                lineColor: '#000000'
                            });
                        }
                    }, 100 * i);
                }
                
                statusDiv.innerHTML = '<div class="status success">✅ 标签生成成功！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 标签生成失败: ${error.message}</div>`;
            }
        }
        
        // 打印标签
        function printLabels() {
            const statusDiv = document.getElementById('label-status');
            const previewArea = document.getElementById('preview-area');
            
            try {
                if (!previewArea.querySelector('.label-item')) {
                    throw new Error('请先生成标签预览');
                }
                
                // 创建打印窗口
                const printWindow = window.open('', '_blank');
                const printContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>产品标签打印</title>
                        <style>
                            @page { margin: 5mm; size: auto; }
                            body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
                            .label-item {
                                width: 60mm; height: 40mm; border: 1px solid #ddd;
                                margin: 2mm; padding: 3mm; background: white;
                                display: inline-block; vertical-align: top;
                                page-break-inside: avoid; box-sizing: border-box;
                            }
                            .product-name { font-size: 14px; font-weight: bold; text-align: center; margin-bottom: 3mm; }
                            .barcode-container { text-align: center; margin: 2mm 0; }
                            .product-info { font-size: 10px; line-height: 1.3; }
                            .product-info div { margin: 1px 0; }
                            @media print {
                                body { margin: 0; padding: 0; }
                                .label-item { margin: 1mm; }
                            }
                        </style>
                    </head>
                    <body>
                        ${previewArea.innerHTML}
                        <script>
                            window.onload = function() {
                                setTimeout(() => {
                                    window.print();
                                    window.close();
                                }, 1000);
                            }
                        </script>
                    </body>
                    </html>
                `;
                
                printWindow.document.write(printContent);
                printWindow.document.close();
                
                statusDiv.innerHTML = '<div class="status success">✅ 打印任务已发送！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 打印失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            setTimeout(checkSystemStatus, 500);
        };
    </script>
</body>
</html>
