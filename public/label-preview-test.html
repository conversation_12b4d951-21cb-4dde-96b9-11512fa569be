<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签预览测试</title>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .label-preview {
            border: 2px solid #ddd;
            margin: 20px 0;
            display: inline-block;
        }
        .test-data {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #337ecc;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>标签预览功能测试</h2>
        
        <div class="test-data">
            <h3>测试模板数据：</h3>
            <pre id="templateData"></pre>
        </div>
        
        <div class="test-data">
            <h3>测试产品数据：</h3>
            <pre id="productData"></pre>
        </div>
        
        <button onclick="testPreview()">生成预览</button>
        <button onclick="clearPreview()">清空预览</button>
        
        <div id="previewResult"></div>
    </div>

    <script>
        // 模拟 LabelGenerator 类
        class LabelGenerator {
            static generateLabelHTML(template, data) {
                const width = template.width || template.labelWidth || 50;
                const height = template.height || template.labelHeight || 30;
                const elements = template.elements || [];
                
                if (!elements || elements.length === 0) {
                    return `
                        <div class="label-container" style="
                            width: ${width}mm;
                            height: ${height}mm;
                            position: relative;
                            background: white;
                            border: 1px solid #ddd;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #999;
                        ">
                            <div>暂无模板元素</div>
                        </div>
                    `;
                }
                
                let html = `
                    <div class="label-container" style="
                        width: ${width}mm;
                        height: ${height}mm;
                        position: relative;
                        background: white;
                        border: 1px solid #ddd;
                    ">
                `;
                
                elements.forEach(element => {
                    html += this.generateElementHTML(element, data);
                });
                
                html += '</div>';
                return html;
            }
            
            static generateElementHTML(element, data) {
                const { type, x, y, width, height } = element;
                const baseStyle = `
                    position: absolute;
                    left: ${x || 0}mm;
                    top: ${y || 0}mm;
                    width: ${width || 20}mm;
                    height: ${height || 10}mm;
                `;
                
                switch (type) {
                    case 'text':
                        return this.generateTextHTML(element, data, baseStyle);
                    case 'barcode':
                        return this.generateBarcodeHTML(element, data, baseStyle);
                    default:
                        return `<div style="${baseStyle} border: 1px dashed #ccc;">未知元素: ${type}</div>`;
                }
            }
            
            static generateTextHTML(element, data, baseStyle) {
                const content = element.dataField ? (data[element.dataField] || '') : (element.content || '');
                const textStyle = `
                    ${baseStyle}
                    font-size: ${element.fontSize || 12}px;
                    color: ${element.color || '#000'};
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                `;
                
                return `<div style="${textStyle}">${content || '(空)'}</div>`;
            }
            
            static generateBarcodeHTML(element, data, baseStyle) {
                const value = element.dataField ? (data[element.dataField] || '') : (element.content || '');
                const barcodeId = `barcode-${element.id}-${Date.now()}`;
                
                if (!value) {
                    return `
                        <div style="${baseStyle} display: flex; align-items: center; justify-content: center; border: 1px dashed #ccc;">
                            <span>无条码数据</span>
                        </div>
                    `;
                }
                
                return `
                    <div style="${baseStyle}">
                        <svg id="${barcodeId}" style="width: 100%; height: 100%;"></svg>
                        <script type="application/json" data-barcode-config>
                            ${JSON.stringify({
                                id: barcodeId,
                                value: value,
                                options: { format: 'CODE128', displayValue: true }
                            })}
                        </script>
                    </div>
                `;
            }
        }

        // 测试数据
        const testTemplate = {
            id: 1,
            name: '测试模板',
            labelWidth: 80,
            labelHeight: 50,
            elements: [
                {
                    id: 'text1',
                    type: 'text',
                    x: 5,
                    y: 5,
                    width: 70,
                    height: 8,
                    content: '',
                    dataField: 'productName',
                    fontSize: 14,
                    color: '#000'
                },
                {
                    id: 'barcode1',
                    type: 'barcode',
                    x: 5,
                    y: 15,
                    width: 70,
                    height: 20,
                    content: '',
                    dataField: 'barCode'
                },
                {
                    id: 'text2',
                    type: 'text',
                    x: 5,
                    y: 38,
                    width: 35,
                    height: 6,
                    content: '',
                    dataField: 'productCode',
                    fontSize: 10,
                    color: '#666'
                },
                {
                    id: 'text3',
                    type: 'text',
                    x: 42,
                    y: 38,
                    width: 33,
                    height: 6,
                    content: '',
                    dataField: 'salePrice',
                    fontSize: 10,
                    color: '#666'
                }
            ]
        };

        const testData = {
            productName: '高品质汽车配件',
            productCode: 'AUTO-PART-001',
            barCode: '6901234567890',
            salePrice: '¥299.99',
            productBrandName: '博世(BOSCH)',
            supplierName: '博世汽配供应商'
        };

        // 显示测试数据
        document.getElementById('templateData').textContent = JSON.stringify(testTemplate, null, 2);
        document.getElementById('productData').textContent = JSON.stringify(testData, null, 2);

        function testPreview() {
            try {
                console.log('开始生成预览...');
                const html = LabelGenerator.generateLabelHTML(testTemplate, testData);
                console.log('生成的HTML:', html);
                
                document.getElementById('previewResult').innerHTML = `
                    <h3>预览结果：</h3>
                    <div class="label-preview">${html}</div>
                `;
                
                // 生成条码
                setTimeout(() => {
                    generateBarcodes();
                }, 100);
                
            } catch (error) {
                console.error('预览失败:', error);
                document.getElementById('previewResult').innerHTML = `
                    <h3>预览失败：</h3>
                    <div style="color: red;">${error.message}</div>
                `;
            }
        }

        function generateBarcodes() {
            const scripts = document.querySelectorAll('[data-barcode-config]');
            scripts.forEach(script => {
                try {
                    const config = JSON.parse(script.textContent);
                    const element = document.getElementById(config.id);
                    if (element && window.JsBarcode) {
                        JsBarcode(element, config.value, config.options);
                    }
                } catch (error) {
                    console.error('生成条码失败:', error);
                }
            });
        }

        function clearPreview() {
            document.getElementById('previewResult').innerHTML = '';
        }

        // 页面加载完成后自动测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testPreview();
        };
    </script>
</body>
</html>
