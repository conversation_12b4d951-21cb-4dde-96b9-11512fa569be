<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JsBarcode 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .label-preview {
            width: 60mm;
            height: 40mm;
            border: 1px solid #ddd;
            margin: 10px;
            padding: 5mm;
            background: white;
            display: inline-block;
            vertical-align: top;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .product-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }
        .barcode-container {
            text-align: center;
            margin: 5px 0;
        }
        .product-info {
            font-size: 10px;
            line-height: 1.2;
        }
        .controls {
            margin: 20px 0;
        }
        .controls input, .controls button {
            margin: 5px;
            padding: 8px;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>JsBarcode 产品标签打印测试</h1>
    
    <div class="test-section">
        <h2>1. JsBarcode 库加载测试</h2>
        <div id="library-status">检查中...</div>
    </div>
    
    <div class="test-section">
        <h2>2. 基础条码生成测试</h2>
        <div class="controls">
            <input type="text" id="barcode-input" value="1234567890128" placeholder="输入条码">
            <button onclick="generateBasicBarcode()">生成条码</button>
        </div>
        <div>
            <canvas id="basic-barcode"></canvas>
        </div>
        <div id="basic-status"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 产品标签预览测试</h2>
        <div class="controls">
            <input type="text" id="product-name" value="测试产品" placeholder="产品名称">
            <input type="text" id="product-code" value="TEST001" placeholder="产品编码">
            <input type="text" id="product-barcode" value="1234567890128" placeholder="产品条码">
            <input type="text" id="product-price" value="99.99" placeholder="产品价格">
            <button onclick="generateProductLabel()">生成标签</button>
        </div>
        <div id="label-container"></div>
        <div id="label-status"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 批量标签生成测试</h2>
        <div class="controls">
            <input type="number" id="label-quantity" value="3" min="1" max="10" placeholder="标签数量">
            <button onclick="generateBatchLabels()">生成批量标签</button>
        </div>
        <div id="batch-container"></div>
        <div id="batch-status"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 打印测试</h2>
        <button onclick="printLabels()">打印当前标签</button>
        <div id="print-status"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        // 检查 JsBarcode 库是否加载成功
        function checkLibraryStatus() {
            const statusDiv = document.getElementById('library-status');
            if (typeof JsBarcode !== 'undefined') {
                statusDiv.innerHTML = '<div class="success">✅ JsBarcode 库加载成功</div>';
                return true;
            } else {
                statusDiv.innerHTML = '<div class="error">❌ JsBarcode 库加载失败</div>';
                return false;
            }
        }
        
        // 生成基础条码
        function generateBasicBarcode() {
            const statusDiv = document.getElementById('basic-status');
            const canvas = document.getElementById('basic-barcode');
            const input = document.getElementById('barcode-input');
            
            try {
                JsBarcode(canvas, input.value, {
                    format: 'CODE128',
                    width: 2,
                    height: 100,
                    displayValue: true,
                    fontSize: 14,
                    textMargin: 2,
                    background: '#ffffff',
                    lineColor: '#000000'
                });
                statusDiv.innerHTML = '<div class="success">✅ 条码生成成功</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 条码生成失败: ${error.message}</div>`;
            }
        }
        
        // 生成产品标签
        function generateProductLabel() {
            const statusDiv = document.getElementById('label-status');
            const container = document.getElementById('label-container');
            
            try {
                const productName = document.getElementById('product-name').value;
                const productCode = document.getElementById('product-code').value;
                const productBarcode = document.getElementById('product-barcode').value;
                const productPrice = document.getElementById('product-price').value;
                
                const labelHTML = `
                    <div class="label-preview">
                        <div class="product-name">${productName}</div>
                        <div class="barcode-container">
                            <canvas class="product-barcode-canvas"></canvas>
                        </div>
                        <div class="product-info">
                            <div>编码: ${productCode}</div>
                            <div>价格: ¥${productPrice}</div>
                        </div>
                    </div>
                `;
                
                container.innerHTML = labelHTML;
                
                // 生成条码
                const canvas = container.querySelector('.product-barcode-canvas');
                JsBarcode(canvas, productBarcode, {
                    format: 'CODE128',
                    width: 1.5,
                    height: 50,
                    displayValue: true,
                    fontSize: 10,
                    textMargin: 1,
                    background: '#ffffff',
                    lineColor: '#000000'
                });
                
                statusDiv.innerHTML = '<div class="success">✅ 产品标签生成成功</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 产品标签生成失败: ${error.message}</div>`;
            }
        }
        
        // 生成批量标签
        function generateBatchLabels() {
            const statusDiv = document.getElementById('batch-status');
            const container = document.getElementById('batch-container');
            
            try {
                const quantity = parseInt(document.getElementById('label-quantity').value);
                const productName = document.getElementById('product-name').value || '测试产品';
                const productCode = document.getElementById('product-code').value || 'TEST001';
                const productBarcode = document.getElementById('product-barcode').value || '1234567890128';
                const productPrice = document.getElementById('product-price').value || '99.99';
                
                let labelsHTML = '';
                for (let i = 0; i < quantity; i++) {
                    labelsHTML += `
                        <div class="label-preview">
                            <div class="product-name">${productName}</div>
                            <div class="barcode-container">
                                <canvas class="batch-barcode-canvas-${i}"></canvas>
                            </div>
                            <div class="product-info">
                                <div>编码: ${productCode}</div>
                                <div>价格: ¥${productPrice}</div>
                                <div>序号: ${i + 1}/${quantity}</div>
                            </div>
                        </div>
                    `;
                }
                
                container.innerHTML = labelsHTML;
                
                // 为每个标签生成条码
                for (let i = 0; i < quantity; i++) {
                    const canvas = container.querySelector(`.batch-barcode-canvas-${i}`);
                    JsBarcode(canvas, productBarcode, {
                        format: 'CODE128',
                        width: 1.5,
                        height: 50,
                        displayValue: true,
                        fontSize: 10,
                        textMargin: 1,
                        background: '#ffffff',
                        lineColor: '#000000'
                    });
                }
                
                statusDiv.innerHTML = `<div class="success">✅ 成功生成 ${quantity} 个标签</div>`;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 批量标签生成失败: ${error.message}</div>`;
            }
        }
        
        // 打印标签
        function printLabels() {
            const statusDiv = document.getElementById('print-status');

            try {
                const batchContainer = document.getElementById('batch-container');
                const labelContainer = document.getElementById('label-container');

                let printContent = '';
                if (batchContainer.innerHTML.trim()) {
                    printContent = batchContainer.innerHTML;
                } else if (labelContainer.innerHTML.trim()) {
                    printContent = labelContainer.innerHTML;
                } else {
                    throw new Error('没有可打印的标签');
                }

                const printWindow = window.open('', '_blank');
                const printHTML = '<!DOCTYPE html><html><head><title>产品标签打印</title><style>@page { margin: 5mm; size: auto; }body { margin: 0; padding: 0; font-family: Arial, sans-serif; }.label-preview { width: 60mm; height: 40mm; border: 1px solid #ddd; margin: 2mm; padding: 2mm; background: white; display: inline-block; vertical-align: top; page-break-inside: avoid; box-sizing: border-box; }.product-name { font-size: 14px; font-weight: bold; text-align: center; margin-bottom: 2mm; }.barcode-container { text-align: center; margin: 2mm 0; }.product-info { font-size: 10px; line-height: 1.2; }@media print { body { margin: 0; padding: 0; } .label-preview { margin: 1mm; } }</style></head><body>' + printContent + '<script>window.onload = function() { window.print(); window.close(); }</script></body></html>';

                printWindow.document.write(printHTML);
                printWindow.document.close();

                statusDiv.innerHTML = '<div class="success">✅ 打印任务已发送</div>';
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ 打印失败: ' + error.message + '</div>';
            }
        }
        
        // 页面加载完成后检查库状态
        window.onload = function() {
            checkLibraryStatus();
        };
    </script>
</body>
</html>
