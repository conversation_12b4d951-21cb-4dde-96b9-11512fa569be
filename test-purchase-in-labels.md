# 采购入库标签打印功能测试

## 实现内容

### 1. 修改的文件
- `src/views/erp/purchase/in/index.vue` - 采购入库页面

### 2. 添加的功能
1. **操作列中的打印标签按钮**
   - 位置：编辑按钮之后
   - 样式：绿色链接按钮
   - 权限：`erp:purchase-in:print-labels`

2. **标签打印处理方法**
   - 方法名：`handlePrintLabels`
   - 功能：获取采购入库单详情，提取产品信息，打开标签预览对话框

3. **组件集成**
   - 导入：`LabelPreviewDialog` 组件
   - 引用：`labelPreviewRef`

### 3. 实现逻辑
1. 点击"打印标签"按钮
2. 检查入库单是否有产品信息
3. 获取入库单详情
4. 提取第一个产品的信息
5. 构造标签数据（产品名称、编码、条码等）
6. 检查条码是否存在
7. 打开标签预览对话框

### 4. 错误处理
- 无产品信息时显示警告
- 无产品明细时显示警告
- 无条码时显示警告
- API调用失败时显示错误信息

### 5. 数据结构
```typescript
const labelData = {
  productId: firstItem.productId,
  productName: firstItem.productName || '未知产品',
  productCode: firstItem.productCode || '',
  barCode: firstItem.productBarCode || '',
  specification: firstItem.productStandard || '',
  price: firstItem.productPrice || 0,
  unit: firstItem.productUnitName || ''
}
```

## 测试步骤

1. 启动项目
2. 进入采购入库页面
3. 查看操作列是否有"打印标签"按钮
4. 点击按钮测试功能
5. 验证标签预览对话框是否正常显示

## 注意事项

1. 需要确保用户有 `erp:purchase-in:print-labels` 权限
2. 需要确保采购入库单有产品明细
3. 需要确保产品有条码信息
4. 操作列宽度已从220px调整为300px以容纳新按钮

## 与参考页面的一致性

该实现完全参考了库存入库页面（`src/views/erp/stock/in/index.vue`）的标签打印功能：
- 相同的按钮样式和位置
- 相同的处理逻辑
- 相同的错误处理机制
- 相同的组件集成方式
