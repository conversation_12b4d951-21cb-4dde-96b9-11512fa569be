<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量标签打印"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div class="multi-label-container">
      <!-- 左侧：设置面板 -->
      <div class="left-panel">
        <!-- 模板设置 -->
        <el-card class="mb-4">
          <template #header>
            <span>模板设置</span>
          </template>
          <el-form :model="printConfig" label-width="80px">
            <el-form-item label="标签模板" required>
              <el-select
                v-model="printConfig.templateId"
                placeholder="请选择标签模板"
                @change="handleTemplateChange"
                class="w-full"
              >
                <el-option
                  v-for="template in templateList"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="打印份数">
              <el-input-number
                v-model="printConfig.copies"
                :min="1"
                :max="100"
                controls-position="right"
                class="w-full"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="openTemplateEditor" size="small">
                <Icon icon="ep:edit" class="mr-5px" /> 调整模板
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 统计信息 -->
        <el-card>
          <template #header>
            <span>打印统计</span>
          </template>
          <div class="print-stats">
            <div class="stat-item">
              <span class="label">产品种类：</span>
              <span class="value">{{ selectedProductCount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">总标签数：</span>
              <span class="value">{{ totalLabels }}</span>
            </div>
            <div class="stat-item">
              <span class="label">总打印数：</span>
              <span class="value">{{ totalPrintCount }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：产品列表和预览 -->
      <div class="right-panel">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 产品列表 -->
          <el-tab-pane label="产品选择" name="products">
            <div class="product-list">
              <!-- 批量操作工具栏 -->
              <div class="toolbar">
                <el-checkbox
                  v-model="selectAll"
                  :indeterminate="isIndeterminate"
                  @change="handleSelectAll"
                >
                  全选
                </el-checkbox>
                <el-button size="small" @click="batchSetQuantity">批量设置数量</el-button>
                <el-button size="small" @click="clearSelection">清空选择</el-button>
              </div>

              <!-- 产品表格 -->
              <el-table 
                :data="productList" 
                stripe 
                max-height="400"
                @selection-change="handleSelectionChange"
                ref="productTableRef"
              >
                <el-table-column type="selection" width="55" :selectable="(row) => row.barCode" />
                <el-table-column prop="productName" label="产品名称" min-width="150" show-overflow-tooltip />
                <el-table-column prop="productCode" label="产品编码" width="120" />
                <el-table-column prop="barCode" label="条码" width="120" />
                <el-table-column prop="specification" label="规格" width="100" show-overflow-tooltip />
                <el-table-column prop="price" label="价格" width="80">
                  <template #default="scope">
                    ¥{{ (scope.row.price || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位" width="60" />
                <el-table-column label="入库数量" width="80">
                  <template #default="scope">
                    {{ scope.row.inQuantity || scope.row.quantity || 0 }}
                  </template>
                </el-table-column>
                <el-table-column prop="sourceNo" label="来源单号" width="120" v-if="sourceInfo.type === 'PURCHASE_IN_BATCH'" />
                <el-table-column label="打印数量" width="120">
                  <template #default="scope">
                    <el-input-number
                      v-model="scope.row.printCount"
                      :min="0"
                      :max="999"
                      size="small"
                      controls-position="right"
                      @change="updateTotalCount"
                      :disabled="!scope.row.selected"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      size="small"
                      @click="previewSingleLabel(scope.row)"
                      :disabled="!scope.row.selected || scope.row.printCount <= 0"
                    >
                      预览
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <!-- 标签预览 -->
          <el-tab-pane label="标签预览" name="preview">
            <div class="preview-container">
              <div class="preview-toolbar">
                <el-button size="small" @click="refreshPreview">刷新预览</el-button>
                <el-button size="small" @click="previewAll">预览全部</el-button>
                <el-button size="small" type="info" @click="debugTemplate">调试模板</el-button>
              </div>
              <div class="preview-content" ref="previewRef">
                <div v-if="previewHtml" v-html="previewHtml" class="label-preview"></div>
                <div v-else class="no-preview">
                  <el-empty description="请选择产品并配置打印数量" />
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePrint" :disabled="!canPrint">
          打印标签 ({{ totalPrintCount }})
        </el-button>
      </div>
    </template>

    <!-- 批量设置数量对话框 -->
    <el-dialog
      v-model="batchQuantityVisible"
      title="批量设置打印数量"
      width="400px"
      append-to-body
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="打印数量">
          <el-input-number
            v-model="batchForm.quantity"
            :min="0"
            :max="999"
            controls-position="right"
            class="w-full"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchQuantityVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchQuantity">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>

  <!-- 模板编辑器对话框 -->
  <LabelTemplateForm ref="templateFormRef" @success="handleTemplateSuccess" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { LabelTemplateApi, LabelTemplateVO, ProductLabelInfo } from '@/api/erp/barcode/label'
import { LabelGenerator, PrintUtils } from '@/utils/barcode'
import { PurchaseInApi } from '@/api/erp/purchase/in'
import { StockInApi } from '@/api/erp/stock/in'
import LabelTemplateForm from '@/views/erp/barcode/template/LabelTemplateForm.vue'
import { BarcodeGenerator } from '@/utils/barcode'

/** 多产品标签打印对话框 */
defineOptions({ name: 'MultiProductLabelDialog' })

const message = useMessage()

// 对话框状态
const dialogVisible = ref(false)
const activeTab = ref('products')
const batchQuantityVisible = ref(false)

// 打印配置
const printConfig = reactive({
  templateId: undefined as number | undefined,
  copies: 1
})

// 数据
const templateList = ref<LabelTemplateVO[]>([])
const productList = ref<ProductLabelInfo[]>([])
const selectedTemplate = ref<LabelTemplateVO | null>(null)
const previewHtml = ref('')
const previewRef = ref()
const productTableRef = ref()
const templateFormRef = ref()

// 选择状态
const selectAll = ref(false)
const selectedProducts = ref<ProductLabelInfo[]>([])

// 批量设置表单
const batchForm = reactive({
  quantity: 1
})

// 来源信息
const sourceInfo = reactive({
  type: '',
  id: 0,
  data: {} as any
})

// 计算属性
const isIndeterminate = computed(() => {
  const selectableProducts = productList.value.filter(p => p.barCode)
  const selectedCount = productList.value.filter(p => p.selected).length
  return selectedCount > 0 && selectedCount < selectableProducts.length
})

const selectedProductCount = computed(() => {
  return productList.value.filter(p => p.selected && p.printCount > 0).length
})

const totalLabels = computed(() => {
  return productList.value
    .filter(p => p.selected)
    .reduce((sum, item) => sum + (item.printCount || 0), 0)
})

const totalPrintCount = computed(() => {
  return totalLabels.value * printConfig.copies
})

const canPrint = computed(() => {
  return printConfig.templateId && totalLabels.value > 0
})

/** 打开对话框 */
const open = async (type: 'STOCK_IN' | 'STOCK_OUT' | 'PURCHASE_IN' | 'PURCHASE_IN_BATCH' | 'PRODUCT', data: any) => {
  dialogVisible.value = true
  sourceInfo.type = type
  sourceInfo.data = data
  
  // 重置状态
  activeTab.value = 'products'
  previewHtml.value = ''
  selectedProducts.value = []
  selectAll.value = false
  
  // 加载模板列表
  await loadTemplates(type)
  
  // 加载产品数据
  await loadProductData(type, data)
}

/** 加载模板列表 */
const loadTemplates = async (type: string) => {
  try {
    // 不区分模板类型，加载所有启用的模板
    const data = await LabelTemplateApi.getLabelTemplateList({ status: 1 })
    templateList.value = data

    // 自动选择默认模板
    const defaultTemplate = data.find(t => t.isDefault)
    if (defaultTemplate) {
      printConfig.templateId = defaultTemplate.id
      await handleTemplateChange()
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  }
}

/** 加载产品数据 */
const loadProductData = async (type: string, data: any) => {
  try {
    let products: ProductLabelInfo[] = []

    if (type === 'STOCK_IN') {
      // 从其他入库单加载产品数据
      const stockInDetail = await StockInApi.getStockIn(data.id)
      products = stockInDetail.items?.map(item => ({
        productId: item.productId,
        productName: item.productName || '未知产品',
        productCode: item.productCode || '',
        barCode: item.productBarCode || '',
        specification: item.productStandard || '',
        price: item.productPrice || 0,
        unit: item.productUnitName || '',
        quantity: item.count || 0,
        inQuantity: item.count || 0,
        printCount: 1,
        selected: false
      })) || []
    } else if (type === 'PURCHASE_IN') {
      // 从采购入库单加载产品数据
      const purchaseInDetail = await PurchaseInApi.getPurchaseIn(data.id)
      products = purchaseInDetail.items?.map(item => ({
        productId: item.productId,
        productName: item.productName || '未知产品',
        productCode: item.productCode || '',
        barCode: item.productBarCode || '',
        specification: item.productStandard || '',
        price: item.productPrice || 0,
        unit: item.productUnitName || '',
        quantity: item.count || 0,
        inQuantity: item.count || 0,
        printCount: 1,
        selected: false
      })) || []
    } else if (type === 'PURCHASE_IN_BATCH') {
      // 批量处理多个采购入库单
      const purchaseInList = Array.isArray(data) ? data : [data]
      products = []
      
      for (const purchaseIn of purchaseInList) {
        if (purchaseIn.items && purchaseIn.items.length > 0) {
          const items = purchaseIn.items.map(item => ({
            productId: item.productId,
            productName: item.productName || '未知产品',
            productCode: item.productCode || '',
            barCode: item.productBarCode || '',
            specification: item.productStandard || '',
            price: item.productPrice || 0,
            unit: item.productUnitName || '',
            quantity: item.count || 0,
            inQuantity: item.count || 0,
            printCount: 1,
            selected: false,
            sourceNo: purchaseIn.no // 添加来源单号标识
          }))
          products.push(...items)
        }
      }
    } else if (type === 'PRODUCT') {
      // 直接使用产品数据
      products = Array.isArray(data) ? data : [data]
      products.forEach(product => {
        product.selected = false
        product.printCount = product.printCount || 1
      })
    }

    // 过滤掉没有条码的产品，并给出提示
    const productsWithBarcode = products.filter(p => p.barCode)
    const productsWithoutBarcode = products.filter(p => !p.barCode)
    
    if (productsWithoutBarcode.length > 0) {
      const productNames = productsWithoutBarcode.map(p => p.productName).join('、')
      message.warning(`以下产品没有条码，将无法打印标签：${productNames}`)
    }

    productList.value = productsWithBarcode
    
    // 重置选择状态
    selectedProducts.value = []
    selectAll.value = false
    
    console.log('加载的产品数据:', productsWithBarcode)
  } catch (error) {
    console.error('加载产品数据失败:', error)
    message.error('加载产品数据失败')
  }
}

/** 模板变更处理 */
const handleTemplateChange = async () => {
  if (!printConfig.templateId) {
    selectedTemplate.value = null
    return
  }

  try {
    // 从API获取完整的模板详情，确保包含元素信息
    const template = await LabelTemplateApi.getLabelTemplate(printConfig.templateId)

    // 解析模板配置，优先使用templateConfig，如果没有则使用template
    const templateContent = template.templateConfig || template.template
    if (templateContent) {
      try {
        const config = JSON.parse(templateContent)
        template.elements = config.elements || []
      } catch (e) {
        console.error('解析模板配置失败:', e)
        template.elements = []
      }
    } else {
      template.elements = []
    }

    console.log('加载的模板详情:', template)
    console.log('模板元素数量:', template.elements?.length || 0)

    selectedTemplate.value = template

    // 清空预览
    previewHtml.value = ''
  } catch (error) {
    console.error('模板变更失败:', error)
    message.error('模板变更失败')
  }
}

/** 全选处理 */
const handleSelectAll = (checked: boolean) => {
  // 只选择有条码的产品
  const selectableProducts = productList.value.filter(p => p.barCode)
  
  if (checked) {
    // 全选：选中所有有条码的产品
    selectableProducts.forEach(product => {
      product.selected = true
    })
    // 更新表格选择状态
    if (productTableRef.value) {
      productTableRef.value.toggleAllSelection()
    }
  } else {
    // 取消全选：取消所有产品的选中状态
    productList.value.forEach(product => {
      product.selected = false
    })
    // 更新表格选择状态
    if (productTableRef.value) {
      productTableRef.value.clearSelection()
    }
  }
  
  updateSelectionState()
}

/** 选择变更处理 */
const handleSelectionChange = (selection: ProductLabelInfo[]) => {
  selectedProducts.value = selection

  // 更新产品的选中状态
  productList.value.forEach(product => {
    product.selected = selection.some(s => s.productId === product.productId)
  })

  updateSelectionState()
}

/** 更新选择状态 */
const updateSelectionState = () => {
  const selectableProducts = productList.value.filter(p => p.barCode)
  const selectedCount = productList.value.filter(p => p.selected).length
  
  if (selectableProducts.length === 0) {
    selectAll.value = false
  } else {
    selectAll.value = selectedCount === selectableProducts.length && selectedCount > 0
  }
}

/** 批量设置数量 */
const batchSetQuantity = () => {
  const selectedCount = productList.value.filter(p => p.selected).length
  if (selectedCount === 0) {
    message.warning('请先选择要设置的产品')
    return
  }
  batchQuantityVisible.value = true
}

/** 确认批量设置数量 */
const confirmBatchQuantity = () => {
  productList.value.forEach(product => {
    if (product.selected) {
      product.printCount = batchForm.quantity
    }
  })
  batchQuantityVisible.value = false
  updateTotalCount()
  message.success('批量设置成功')
}

/** 清空选择 */
const clearSelection = () => {
  productList.value.forEach(product => {
    product.selected = false
    product.printCount = 0
  })
  selectedProducts.value = []
  selectAll.value = false
  updateTotalCount()
}

/** 更新总数量 */
const updateTotalCount = () => {
  // 触发计算属性更新
}

/** 预览单个标签 */
const previewSingleLabel = async (product: ProductLabelInfo) => {
  if (!selectedTemplate.value) {
    message.warning('请先选择模板')
    return
  }

  if (!product.barCode) {
    message.warning('该产品没有条码，无法预览标签')
    return
  }

  try {
    console.log('预览单个标签 - 选中的模板:', selectedTemplate.value)
    console.log('预览单个标签 - 模板元素数量:', selectedTemplate.value?.elements?.length || 0)

    const labelData = buildLabelData(product)
    console.log('预览单个标签 - 标签数据:', labelData)

    previewHtml.value = LabelGenerator.generateLabelHTML(selectedTemplate.value, labelData)
    activeTab.value = 'preview'

    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error(`生成预览失败: ${error.message || error}`)
  }
}

/** 刷新预览 */
const refreshPreview = async () => {
  if (!selectedTemplate.value) {
    previewHtml.value = ''
    return
  }

  const selectedProductsWithCount = productList.value.filter(p => p.selected && p.printCount > 0)
  if (selectedProductsWithCount.length === 0) {
    previewHtml.value = ''
    return
  }

  try {
    console.log('刷新预览 - 选中的模板:', selectedTemplate.value)
    console.log('刷新预览 - 模板元素数量:', selectedTemplate.value?.elements?.length || 0)

    // 生成第一个选中产品的标签预览
    const firstProduct = selectedProductsWithCount[0]
    const labelData = buildLabelData(firstProduct)

    console.log('刷新预览 - 标签数据:', labelData)

    previewHtml.value = LabelGenerator.generateLabelHTML(selectedTemplate.value, labelData)

    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error(`生成预览失败: ${error.message || error}`)
  }
}

/** 调试模板信息 */
const debugTemplate = () => {
  console.log('=== 模板调试信息 ===')
  console.log('当前选中模板ID:', printConfig.templateId)
  console.log('当前选中模板对象:', selectedTemplate.value)
  console.log('模板元素数量:', selectedTemplate.value?.elements?.length || 0)
  console.log('模板元素详情:', selectedTemplate.value?.elements)
  console.log('模板配置字段:', selectedTemplate.value?.templateConfig)
  console.log('模板内容字段:', selectedTemplate.value?.template)
  console.log('选中的字段:', selectedFields.value)

  const selectedProducts = productList.value.filter(p => p.selected && p.printCount > 0)
  if (selectedProducts.length > 0) {
    const firstProduct = selectedProducts[0]
    console.log('第一个选中产品:', firstProduct)
    console.log('构建的标签数据:', buildLabelData(firstProduct))
  }

  message.info('调试信息已输出到控制台，请按F12查看')
}

/** 预览全部标签 */
const previewAll = async () => {
  if (!selectedTemplate.value) {
    message.warning('请先选择模板')
    return
  }

  const selectedProductsWithCount = productList.value.filter(p => p.selected && p.printCount > 0)
  if (selectedProductsWithCount.length === 0) {
    message.warning('请选择产品并设置打印数量')
    return
  }

  try {
    const allLabelsHtml = selectedProductsWithCount
      .map(product => {
        const labelData = buildLabelData(product)
        const labels = []
        for (let i = 0; i < product.printCount; i++) {
          labels.push(LabelGenerator.generateLabelHTML(selectedTemplate.value!, labelData))
        }
        return labels.join('')
      })
      .join('')

    previewHtml.value = allLabelsHtml

    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error('生成预览失败')
  }
}

/** 构建标签数据 */
const buildLabelData = (product: ProductLabelInfo) => {
  const data: Record<string, any> = {
    // 产品相关字段
    productId: product.productId,
    productName: product.productName,
    productCode: product.productCode,
    barCode: product.barCode,
    universalProductCode: product.productCode, // 通用产品码，使用产品编码
    specification: product.specification,
    price: product.price,
    unit: product.unit,
    
    // 入库相关字段
    stockInNo: sourceInfo.data?.no || '',
    inTime: new Date().toLocaleDateString(),
    warehouseName: '主仓库',
    supplierName: sourceInfo.data?.supplierName || '',
    
    // 系统字段
    currentDate: new Date().toLocaleDateString(),
    currentTime: new Date().toLocaleString(),
    printTime: new Date().toLocaleString(),
    
    // 兼容旧字段
    salePrice: product.price,
    purchasePrice: product.price,
    minPrice: product.price,
    weight: '',
    productBrandName: '',
    categoryName: '',
    unitName: product.unit
  }

  return data
}

/** 在预览中生成条码 */
const generateBarcodesInPreview = () => {
  if (!previewRef.value) return

  // 查找所有条码配置脚本
  const barcodeScripts = previewRef.value.querySelectorAll('[data-barcode-config]')
  barcodeScripts.forEach((script: HTMLElement) => {
    try {
      const config = JSON.parse(script.textContent || '{}')
      const element = document.getElementById(config.id)
      if (element) {
        // 使用内置的条码生成器
        const svg = BarcodeGenerator.generateBarcodeSVG(config.value, config.options)
        element.innerHTML = svg
      }
    } catch (error) {
      console.error('生成条码失败:', error)
      // 显示错误信息
      const config = JSON.parse(script.textContent || '{}')
      const element = document.getElementById(config?.id)
      if (element) {
        element.innerHTML = '<text x="50%" y="50%" text-anchor="middle" fill="red">条码生成失败</text>'
      }
    }
  })
}

/** 执行打印 */
const handlePrint = async () => {
  if (!canPrint.value) {
    message.warning('请配置打印信息')
    return
  }

  const selectedProductsWithCount = productList.value.filter(p => p.selected && p.printCount > 0)
  if (selectedProductsWithCount.length === 0) {
    message.warning('请选择产品并设置打印数量')
    return
  }

  // 检查所有选中产品是否都有条码
  const productsWithoutBarcode = selectedProductsWithCount.filter(p => !p.barCode)
  if (productsWithoutBarcode.length > 0) {
    const productNames = productsWithoutBarcode.map(p => p.productName).join('、')
    message.warning(`以下产品没有条码，无法打印：${productNames}`)
    return
  }

  try {
    // 生成所有标签HTML
    const allLabelsHtml = []

    for (const product of selectedProductsWithCount) {
      const labelData = buildLabelData(product)

      // 根据份数和数量生成标签
      for (let copy = 0; copy < printConfig.copies; copy++) {
        for (let count = 0; count < product.printCount; count++) {
          allLabelsHtml.push(LabelGenerator.generateLabelHTML(selectedTemplate.value!, labelData))
        }
      }
    }

    if (allLabelsHtml.length === 0) {
      message.warning('没有需要打印的标签')
      return
    }

    // 执行打印
    const printHtml = allLabelsHtml.join('')
    PrintUtils.printHTML(printHtml, '产品标签打印')

    message.success('打印任务已发送')

    // 关闭对话框
    dialogVisible.value = false
  } catch (error) {
    console.error('打印失败:', error)
    message.error('打印失败')
  }
}

/** 打开模板编辑器 */
const openTemplateEditor = () => {
  if (!selectedTemplate.value) {
    message.warning('请先选择一个模板')
    return
  }
  templateFormRef.value?.open('update', selectedTemplate.value.id)
}

/** 模板编辑成功回调 */
const handleTemplateSuccess = () => {
  // 刷新模板列表
  loadTemplates(sourceInfo.type)
  // 重新加载当前模板
  handleTemplateChange()
  message.success('模板编辑成功')
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.multi-label-container {
  display: flex;
  gap: 16px;
  height: 600px;
}

.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.field-settings {
  max-height: 200px;
  overflow-y: auto;
}

.field-item {
  margin-bottom: 8px;
}

.print-stats {
  space-y: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item .label {
  color: #666;
}

.stat-item .value {
  font-weight: bold;
  color: #409eff;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.product-list {
  padding: 16px;
}

.preview-container {
  padding: 16px;
}

.preview-toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.preview-content {
  border: 1px dashed #ddd;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
  min-height: 300px;
}

.no-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.label-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.w-full {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
