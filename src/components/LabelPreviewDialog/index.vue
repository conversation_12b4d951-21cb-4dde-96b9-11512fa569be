<template>
  <Dialog v-model="dialogVisible" title="标签预览" width="800px">
    <div class="label-preview-container">
      <!-- 预览区域 -->
      <div class="preview-section">
        <div class="preview-header">
          <el-form :model="previewForm" inline>
            <el-form-item label="打印数量:">
              <el-input-number
                v-model="previewForm.quantity"
                :min="1"
                :max="100"
                size="small"
                style="width: 120px"
              />
            </el-form-item>
            <el-form-item>
              <el-button size="small" @click="refreshPreview">刷新预览</el-button>
              <el-button type="primary" size="small" @click="handlePrint">打印</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="preview-content">
          <div
            v-for="(item, index) in previewItems"
            :key="index"
            class="label-item"
            :style="labelStyle"
          >
            <!-- 产品名称 -->
            <div class="product-name">{{ item.productName }}</div>
            
            <!-- 条码区域 -->
            <div class="barcode-container">
              <canvas
                :ref="el => setBarcodeRef(el, index)"
                class="barcode-canvas"
              ></canvas>
            </div>
            
            <!-- 产品信息 -->
            <div class="product-info">
              <div class="product-code">编码: {{ item.productCode }}</div>
              <div class="product-spec" v-if="item.specification">
                规格: {{ item.specification }}
              </div>
              <div class="product-price" v-if="item.price">
                价格: ¥{{ item.price.toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印标签</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { BarcodeGenerator, type BarcodeOptions } from '@/utils/barcode'

// 标签数据接口
interface LabelData {
  productId?: number
  productName: string
  productCode: string
  barCode: string
  specification?: string
  price?: number
  unit?: string
}

// 组件事件
const emit = defineEmits<{
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const barcodeRefs = ref<HTMLCanvasElement[]>([])
const labelData = ref<LabelData>({
  productName: '',
  productCode: '',
  barCode: ''
})

// 预览表单
const previewForm = reactive({
  quantity: 1
})

// 预览项目列表
const previewItems = computed(() => {
  return Array(previewForm.quantity).fill(labelData.value)
})

// 标签样式
const labelStyle = computed(() => ({
  width: '60mm',
  height: '40mm',
  border: '1px solid #ddd',
  margin: '5mm',
  padding: '2mm',
  backgroundColor: 'white',
  display: 'inline-block',
  verticalAlign: 'top',
  pageBreakInside: 'avoid'
}))

/**
 * 设置条码引用
 */
const setBarcodeRef = (el: HTMLCanvasElement | null, index: number) => {
  if (el) {
    barcodeRefs.value[index] = el
  }
}

/**
 * 打开对话框
 */
const open = (data: LabelData) => {
  dialogVisible.value = true
  labelData.value = { ...data }
  
  nextTick(() => {
    generateBarcodes()
  })
}

/**
 * 生成条码
 */
const generateBarcodes = () => {
  nextTick(() => {
    barcodeRefs.value.forEach((canvas, index) => {
      if (canvas && labelData.value.barCode) {
        try {
          const options: BarcodeOptions = {
            format: 'CODE128',
            width: 2,
            height: 60,
            displayValue: true,
            fontSize: 12,
            textMargin: 2,
            background: '#ffffff',
            lineColor: '#000000'
          }
          BarcodeGenerator.generate(canvas, labelData.value.barCode, options)
        } catch (error) {
          console.error(`生成第${index + 1}个条码失败:`, error)
        }
      }
    })
  })
}

/**
 * 刷新预览
 */
const refreshPreview = () => {
  // 清空现有引用
  barcodeRefs.value = []
  
  nextTick(() => {
    generateBarcodes()
  })
}

/**
 * 打印标签
 */
const handlePrint = () => {
  if (!labelData.value.barCode) {
    ElMessage.warning('条码不能为空')
    return
  }
  
  try {
    // 生成打印HTML
    const printHTML = generatePrintHTML()
    
    // 打开打印窗口
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      ElMessage.error('无法打开打印窗口，请检查浏览器弹窗设置')
      return
    }
    
    printWindow.document.write(printHTML)
    printWindow.document.close()
    
    // 等待内容加载完成后打印
    printWindow.onload = () => {
      // 使用本地 BarcodeGenerator 生成条码
      const canvases = printWindow.document.querySelectorAll('.barcode-canvas')
      let completedCount = 0
      const totalCount = canvases.length

      if (totalCount === 0) {
        // 没有条码，直接打印
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 100)
        return
      }

      canvases.forEach((canvas: HTMLCanvasElement, index: number) => {
        try {
          // 使用当前页面的 BarcodeGenerator 实例生成条码
          const tempCanvas = document.createElement('canvas')
          const options: BarcodeOptions = {
            format: 'CODE128',
            width: 2,
            height: 60,
            displayValue: true,
            fontSize: 12,
            textMargin: 2,
            background: '#ffffff',
            lineColor: '#000000'
          }

          BarcodeGenerator.generate(tempCanvas, labelData.value.barCode, options)

          // 将生成的条码复制到打印窗口的 canvas
          const ctx = canvas.getContext('2d')
          if (ctx && tempCanvas) {
            canvas.width = tempCanvas.width
            canvas.height = tempCanvas.height
            ctx.drawImage(tempCanvas, 0, 0)
          }

          completedCount++

          // 所有条码生成完成后开始打印
          if (completedCount === totalCount) {
            setTimeout(() => {
              printWindow.print()
              printWindow.close()
            }, 300)
          }
        } catch (error) {
          console.error(`生成打印条码 ${index + 1} 失败:`, error)
          completedCount++

          // 即使有错误也要检查是否完成
          if (completedCount === totalCount) {
            setTimeout(() => {
              printWindow.print()
              printWindow.close()
            }, 300)
          }
        }
      })
    }
    
    ElMessage.success('打印任务已发送')
    emit('success')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败')
  }
}

/**
 * 生成打印HTML
 */
const generatePrintHTML = (): string => {
  const items = Array(previewForm.quantity).fill(labelData.value)
  
  const itemsHTML = items.map((item, index) => `
    <div class="label-item">
      <div class="product-name">${item.productName}</div>
      <div class="barcode-container">
        <canvas class="barcode-canvas"></canvas>
      </div>
      <div class="product-info">
        <div class="product-code">编码: ${item.productCode}</div>
        ${item.specification ? `<div class="product-spec">规格: ${item.specification}</div>` : ''}
        ${item.price ? `<div class="product-price">价格: ¥${item.price.toFixed(2)}</div>` : ''}
      </div>
    </div>
  `).join('')
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>产品标签打印</title>
      <style>
        @page {
          margin: 5mm;
          size: auto;
        }
        
        body {
          margin: 0;
          padding: 0;
          font-family: Arial, sans-serif;
        }
        
        .label-item {
          width: 60mm;
          height: 40mm;
          border: 1px solid #ddd;
          margin: 2mm;
          padding: 2mm;
          background: white;
          display: inline-block;
          vertical-align: top;
          page-break-inside: avoid;
          box-sizing: border-box;
        }
        
        .product-name {
          font-size: 14px;
          font-weight: bold;
          text-align: center;
          margin-bottom: 2mm;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .barcode-container {
          text-align: center;
          margin: 2mm 0;
        }
        
        .barcode-canvas {
          max-width: 100%;
          height: auto;
        }
        
        .product-info {
          font-size: 10px;
          line-height: 1.2;
        }
        
        .product-code,
        .product-spec,
        .product-price {
          margin: 1mm 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        @media print {
          body { margin: 0; padding: 0; }
          .label-item { margin: 1mm; }
        }
      </style>
    </head>
    <body>
      ${itemsHTML}
    </body>
    </html>
  `
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.label-preview-container {
  max-height: 600px;
  overflow-y: auto;
}

.preview-section {
  padding: 16px;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.preview-content {
  background: #f5f5f5;
  padding: 16px;
  border: 1px dashed #ddd;
  text-align: center;
}

.label-item {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.product-name {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.barcode-container {
  text-align: center;
  margin: 8px 0;
}

.barcode-canvas {
  max-width: 100%;
  height: auto;
}

.product-info {
  font-size: 12px;
  line-height: 1.4;
}

.product-code,
.product-spec,
.product-price {
  margin: 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  text-align: right;
}
</style>
