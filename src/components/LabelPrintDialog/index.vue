<template>
  <el-dialog
    v-model="dialogVisible"
    title="标签打印"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="label-print-container">
      <!-- 左侧：模板选择和设置 -->
      <div class="left-panel">
        <el-card class="mb-4">
          <template #header>
            <span>模板设置</span>
          </template>
          <el-form :model="printConfig" label-width="80px">
            <el-form-item label="标签模板" required>
              <el-select
                v-model="printConfig.templateId"
                placeholder="请选择标签模板"
                @change="handleTemplateChange"
                class="w-full"
              >
                <el-option
                  v-for="template in templateList"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="打印份数">
              <el-input-number
                v-model="printConfig.copies"
                :min="1"
                :max="100"
                controls-position="right"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="打印设置">
              <el-checkbox v-model="printConfig.autoClose">打印后自动关闭</el-checkbox>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 统计信息 -->
        <el-card>
          <template #header>
            <span>打印统计</span>
          </template>
          <div class="print-stats">
            <div class="stat-item">
              <span class="label">产品种类：</span>
              <span class="value">{{ productList.length }}</span>
            </div>
            <div class="stat-item">
              <span class="label">总标签数：</span>
              <span class="value">{{ totalLabels }}</span>
            </div>
            <div class="stat-item">
              <span class="label">总打印数：</span>
              <span class="value">{{ totalPrintCount }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：产品列表和预览 -->
      <div class="right-panel">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 产品列表 -->
          <el-tab-pane label="产品列表" name="products">
            <div class="product-list">
              <el-table :data="productList" stripe max-height="400">
                <el-table-column prop="productName" label="产品名称" min-width="150" />
                <el-table-column prop="productCode" label="产品编码" width="120" />
                <el-table-column prop="barCode" label="条码" width="120" />
                <el-table-column prop="salePrice" label="价格" width="80">
                  <template #default="scope">
                    ¥{{ scope.row.salePrice || 0 }}
                  </template>
                </el-table-column>
                <el-table-column label="打印数量" width="120">
                  <template #default="scope">
                    <el-input-number
                      v-model="scope.row.printCount"
                      :min="0"
                      :max="999"
                      size="small"
                      controls-position="right"
                      @change="updateTotalCount"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      size="small"
                      @click="previewSingleLabel(scope.row)"
                    >
                      预览
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <!-- 标签预览 -->
          <el-tab-pane label="标签预览" name="preview">
            <div class="preview-container">
              <div class="preview-toolbar">
                <el-button size="small" @click="refreshPreview">刷新预览</el-button>
                <el-button size="small" @click="previewAll">预览全部</el-button>
              </div>
              <div class="preview-content" ref="previewRef">
                <div v-if="previewHtml" v-html="previewHtml" class="label-preview"></div>
                <div v-else class="no-preview">
                  <el-empty description="请选择模板并配置产品信息" />
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePreviewAll">预览全部</el-button>
        <el-button type="success" @click="handlePrint" :disabled="!canPrint">
          开始打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { LabelTemplateApi, LabelTemplateVO, LabelPrintApi, ProductLabelInfo } from '@/api/erp/barcode/label'
import { LabelGenerator, PrintUtils } from '@/utils/barcode'

/** 标签打印对话框 */
defineOptions({ name: 'LabelPrintDialog' })

const message = useMessage()

// 对话框状态
const dialogVisible = ref(false)
const activeTab = ref('products')

// 打印配置
const printConfig = reactive({
  templateId: undefined as number | undefined,
  copies: 1,
  autoClose: true
})

// 数据
const templateList = ref<LabelTemplateVO[]>([])
const productList = ref<ProductLabelInfo[]>([])
const selectedTemplate = ref<LabelTemplateVO | null>(null)
const previewHtml = ref('')
const previewRef = ref()

// 来源信息
const sourceInfo = reactive({
  type: '',
  id: 0,
  data: {} as any
})

// 计算属性
const totalLabels = computed(() => {
  return productList.value.reduce((sum, item) => sum + (item.printCount || 0), 0)
})

const totalPrintCount = computed(() => {
  return totalLabels.value * printConfig.copies
})

const canPrint = computed(() => {
  return printConfig.templateId && totalLabels.value > 0
})

/** 打开对话框 */
const open = async (type: 'STOCK_IN' | 'STOCK_OUT' | 'PRODUCT', data: any) => {
  dialogVisible.value = true
  sourceInfo.type = type
  sourceInfo.data = data
  
  // 重置状态
  activeTab.value = 'products'
  previewHtml.value = ''
  
  // 加载模板列表
  await loadTemplates(type)
  
  // 加载产品数据
  await loadProductData(type, data)
}

/** 加载模板列表 */
const loadTemplates = async (type: string) => {
  try {
    // 不区分模板类型，加载所有启用的模板
    const data = await LabelTemplateApi.getLabelTemplateList({ status: 1 })
    templateList.value = data

    // 自动选择默认模板
    const defaultTemplate = data.find(t => t.isDefault)
    if (defaultTemplate) {
      printConfig.templateId = defaultTemplate.id
      await handleTemplateChange()
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  }
}

/** 加载产品数据 */
const loadProductData = async (type: string, data: any) => {
  try {
    let products: ProductLabelInfo[] = []
    
    if (type === 'STOCK_IN') {
      // 从入库单加载产品数据
      products = await loadProductsFromStockIn(data.id)
    } else if (type === 'STOCK_OUT') {
      // 从出库单加载产品数据
      products = await loadProductsFromStockOut(data.id)
    } else if (type === 'PRODUCT') {
      // 直接使用产品数据
      products = Array.isArray(data) ? data : [data]
    }
    
    // 设置默认打印数量
    products.forEach(product => {
      product.printCount = product.quantity || 1
    })
    
    productList.value = products
  } catch (error) {
    console.error('加载产品数据失败:', error)
    message.error('加载产品数据失败')
  }
}

/** 从入库单加载产品 */
const loadProductsFromStockIn = async (stockInId: number): Promise<ProductLabelInfo[]> => {
  // TODO: 实现从入库单加载产品逻辑
  // 这里需要调用后端API获取入库单的产品明细
  return []
}

/** 从出库单加载产品 */
const loadProductsFromStockOut = async (stockOutId: number): Promise<ProductLabelInfo[]> => {
  // TODO: 实现从出库单加载产品逻辑
  return []
}

/** 模板变化处理 */
const handleTemplateChange = async () => {
  if (!printConfig.templateId) {
    selectedTemplate.value = null
    return
  }

  try {
    const template = await LabelTemplateApi.getLabelTemplate(printConfig.templateId)

    // 解析模板配置，优先使用templateConfig，如果没有则使用template
    const templateContent = template.templateConfig || template.template
    if (templateContent) {
      try {
        const config = JSON.parse(templateContent)
        template.elements = config.elements || []
      } catch (e) {
        console.error('解析模板配置失败:', e)
        template.elements = []
      }
    } else {
      template.elements = []
    }

    console.log('LabelPrintDialog 加载的模板详情:', template)
    console.log('模板元素数量:', template.elements?.length || 0)

    selectedTemplate.value = template

    // 刷新预览
    await refreshPreview()
  } catch (error) {
    console.error('获取模板失败:', error)
    message.error('获取模板失败')
  }
}

/** 刷新预览 */
const refreshPreview = async () => {
  if (!selectedTemplate.value || productList.value.length === 0) {
    previewHtml.value = ''
    return
  }
  
  try {
    // 生成第一个产品的标签预览
    const firstProduct = productList.value[0]
    const labelData = buildLabelData(firstProduct)
    
    previewHtml.value = LabelGenerator.generateLabelHTML(selectedTemplate.value, labelData)
    
    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error('生成预览失败')
  }
}

/** 预览全部标签 */
const previewAll = async () => {
  if (!selectedTemplate.value || productList.value.length === 0) {
    message.warning('请选择模板并配置产品信息')
    return
  }
  
  try {
    const allLabelsHtml = productList.value
      .filter(product => product.printCount > 0)
      .map(product => {
        const labelData = buildLabelData(product)
        const labels = []
        for (let i = 0; i < product.printCount; i++) {
          labels.push(LabelGenerator.generateLabelHTML(selectedTemplate.value!, labelData))
        }
        return labels.join('')
      })
      .join('')
    
    previewHtml.value = allLabelsHtml
    activeTab.value = 'preview'
    
    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error('生成预览失败')
  }
}

/** 预览单个标签 */
const previewSingleLabel = async (product: ProductLabelInfo) => {
  if (!selectedTemplate.value) {
    message.warning('请先选择模板')
    return
  }
  
  try {
    const labelData = buildLabelData(product)
    previewHtml.value = LabelGenerator.generateLabelHTML(selectedTemplate.value, labelData)
    activeTab.value = 'preview'
    
    // 等待DOM更新后生成条码
    await nextTick()
    generateBarcodesInPreview()
  } catch (error) {
    console.error('生成预览失败:', error)
    message.error('生成预览失败')
  }
}

/** 构建标签数据 */
const buildLabelData = (product: ProductLabelInfo): Record<string, any> => {
  const now = new Date()
  return {
    // 产品信息
    productId: product.productId,
    productName: product.productName,
    productCode: product.productCode,
    barCode: product.barCode,
    salePrice: product.customData?.salePrice || 0,
    purchasePrice: product.customData?.purchasePrice || 0,
    standard: product.customData?.standard || '',
    weight: product.customData?.weight || 0,
    productBrandName: product.customData?.productBrandName || '',
    
    // 来源信息
    stockInNo: sourceInfo.data.no || '',
    stockOutNo: sourceInfo.data.no || '',
    inTime: sourceInfo.data.inTime ? new Date(sourceInfo.data.inTime).toLocaleDateString() : '',
    outTime: sourceInfo.data.outTime ? new Date(sourceInfo.data.outTime).toLocaleDateString() : '',
    warehouseName: sourceInfo.data.warehouseName || '',
    supplierName: sourceInfo.data.supplierName || '',
    customerName: sourceInfo.data.customerName || '',
    
    // 系统信息
    currentDate: now.toLocaleDateString(),
    currentTime: now.toLocaleString(),
    printTime: now.toLocaleString(),
    
    // 自定义数据
    ...product.customData
  }
}

/** 在预览中生成条码 */
const generateBarcodesInPreview = () => {
  if (!previewRef.value) return
  
  // 查找所有条码配置脚本
  const barcodeScripts = previewRef.value.querySelectorAll('[data-barcode-config]')
  barcodeScripts.forEach((script: HTMLElement) => {
    try {
      const config = JSON.parse(script.textContent || '{}')
      const element = document.getElementById(config.id)
      if (element && window.JsBarcode) {
        window.JsBarcode(element, config.value, config.options)
      }
    } catch (error) {
      console.error('生成条码失败:', error)
    }
  })
}

/** 更新总数量 */
const updateTotalCount = () => {
  // 触发计算属性更新
}

/** 预览全部 */
const handlePreviewAll = () => {
  previewAll()
}

/** 执行打印 */
const handlePrint = async () => {
  if (!canPrint.value) {
    message.warning('请配置打印信息')
    return
  }
  
  try {
    // 生成所有标签HTML
    const allLabelsHtml = []
    
    for (const product of productList.value) {
      if (product.printCount <= 0) continue
      
      const labelData = buildLabelData(product)
      
      // 根据份数和数量生成标签
      for (let copy = 0; copy < printConfig.copies; copy++) {
        for (let count = 0; count < product.printCount; count++) {
          allLabelsHtml.push(LabelGenerator.generateLabelHTML(selectedTemplate.value!, labelData))
        }
      }
    }
    
    if (allLabelsHtml.length === 0) {
      message.warning('没有需要打印的标签')
      return
    }
    
    // 执行打印
    const printHtml = allLabelsHtml.join('')
    PrintUtils.printHTML(printHtml, '产品标签打印')
    
    message.success('打印任务已发送')
    
    // 自动关闭对话框
    if (printConfig.autoClose) {
      dialogVisible.value = false
    }
  } catch (error) {
    console.error('打印失败:', error)
    message.error('打印失败')
  }
}

/** 暴露方法 */
defineExpose({ open })
</script>

<style scoped>
.label-print-container {
  display: flex;
  gap: 16px;
  height: 60vh;
}

.left-panel {
  width: 300px;
  overflow-y: auto;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.print-stats {
  space-y: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-item .label {
  color: #666;
}

.stat-item .value {
  font-weight: bold;
  color: #409eff;
}

.product-list {
  height: 400px;
  overflow-y: auto;
}

.preview-container {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 8px;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #ddd;
  background: #f5f5f5;
  padding: 16px;
}

.label-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
