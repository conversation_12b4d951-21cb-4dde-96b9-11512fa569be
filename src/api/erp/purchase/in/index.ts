import request from '@/config/axios'

// ERP 采购入库 VO
export interface PurchaseInVO {
  id: number // 入库工单编号
  no: string // 采购入库号
  supplierId: number // 供应商编号
  supplierName: string // 供应商名称
  accountId: number // 结算账户编号
  inTime: Date // 入库时间
  orderId: number // 采购订单编号
  orderNo: string // 采购订单号
  totalCount: number // 合计数量
  totalPrice: number // 合计金额，单位：元
  paymentPrice: number // 已付款金额，单位：元
  totalProductPrice: number // 合计产品价格，单位：元
  totalTaxPrice: number // 合计税额，单位：元
  discountPercent: number // 优惠率，百分比
  discountPrice: number // 优惠金额，单位：元
  otherPrice: number // 定金金额，单位：元
  fileUrl: string // 附件地址
  status: number // 状态
  remark: string // 备注
  creator: string // 创建人
  creatorName: string // 创建人名称
  createTime: Date // 创建时间
  items: PurchaseInItemVO[] // 入库项列表
  productNames: string // 产品信息
  productCodes: string // 通用产品码信息
}

// ERP 采购入库项 VO
export interface PurchaseInItemVO {
  id: number // 入库项编号
  orderItemId: number // 采购订单项编号
  warehouseId: number // 仓库编号
  productId: number // 产品编号
  productUnitId: number // 产品单位单位
  productPrice: number // 产品单价
  count: number // 产品数量
  taxPercent: number // 税率，百分比
  taxPrice: number // 税额，单位：元
  remark: string // 备注
  // 关联字段
  productName: string // 产品名称
  productBarCode: string // 产品条码
  productCode: string // 通用产品码
  productUnitName: string // 产品单位名称
  stockCount: number // 库存数量
}

// ERP 采购入库 API
export const PurchaseInApi = {
  // 查询采购入库分页
  getPurchaseInPage: async (params: any) => {
    return await request.get({ url: `/erp/purchase-in/page`, params })
  },

  // 查询采购入库详情
  getPurchaseIn: async (id: number) => {
    return await request.get({ url: `/erp/purchase-in/get?id=` + id })
  },

  // 批量查询采购入库详情
  getPurchaseInListByIds: async (ids: number[]) => {
    return await request.post({ url: `/erp/purchase-in/get-list`, data: { ids } })
  },

  // 新增采购入库
  createPurchaseIn: async (data: PurchaseInVO) => {
    return await request.post({ url: `/erp/purchase-in/create`, data })
  },

  // 修改采购入库
  updatePurchaseIn: async (data: PurchaseInVO) => {
    return await request.put({ url: `/erp/purchase-in/update`, data })
  },

  // 更新采购入库的状态
  updatePurchaseInStatus: async (id: number, status: number) => {
    return await request.put({
      url: `/erp/purchase-in/update-status`,
      params: {
        id,
        status
      }
    })
  },

  // 删除采购入库
  deletePurchaseIn: async (ids: number[]) => {
    return await request.delete({
      url: `/erp/purchase-in/delete`,
      params: {
        ids: ids.join(',')
      }
    })
  },

  // 导出采购入库 Excel
  exportPurchaseIn: async (params: any) => {
    return await request.download({ url: `/erp/purchase-in/export-excel`, params })
  }
}
