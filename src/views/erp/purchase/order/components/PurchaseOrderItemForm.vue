<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" show-summary :summary-method="getSummaries" class="-mt-10px">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="通用产品码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productCode`" class="mb-0px!">
            <el-select
              v-model="row.productCode"
              clearable
              filterable
              :filter-method="(query) => filterProductCode(query, $index)"
              @change="onChangeProductCode($event, row)"
              placeholder="请选择通用产品码"
            >
              <el-option
                v-for="item in getFilteredProductCodeList($index)"
                :key="item.productCode"
                :label="`${item.productCode} - ${item.name}`"
                :value="item.productCode"
              >
                <div style="display: flex; flex-direction: column; line-height: 1.2;">
                  <span style="font-weight: 500; color: #303133;">{{ item.productCode }}</span>
                  <span style="font-size: 12px; color: #909399;">{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              @change="onChangeProduct($event, row)"
              placeholder="请选择产品"
              :disabled="!!row.productCode"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="库存" min-width="100">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.stockCount" :formatter="erpCountInputFormatter" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="条码" min-width="150">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productBarCode" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="80">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productUnitName" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="count" fixed="right" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.count`" :rules="formRules.count" class="mb-0px!">
            <el-input-number
              v-model="row.count"
              controls-position="right"
              :min="0.001"
              :precision="3"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品单价" fixed="right" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.productPrice`"
            :rules="formRules.productPrice"
            class="mb-0px!"
          >
            <el-input-number
              v-model="row.productPrice"
              controls-position="right"
              :min="0.01"
              :precision="2"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金额" prop="totalProductPrice" fixed="right" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalProductPrice`" class="mb-0px!">
            <el-input
              disabled
              v-model="row.totalProductPrice"
              :formatter="erpPriceInputFormatter"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="税率（%）" fixed="right" min-width="115">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPercent`" class="mb-0px!">
            <el-input-number
              v-model="row.taxPercent"
              controls-position="right"
              :min="0"
              :precision="2"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="税额" prop="taxPrice" fixed="right" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" class="mb-0px!">
            <el-form-item :prop="`${$index}.taxPrice`" class="mb-0px!">
              <el-input disabled v-model="row.taxPrice" :formatter="erpPriceInputFormatter" />
            </el-form-item>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="税额合计" prop="totalPrice" fixed="right" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalPrice`" class="mb-0px!">
            <el-input disabled v-model="row.totalPrice" :formatter="erpPriceInputFormatter" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加采购产品</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { StockApi } from '@/api/erp/stock/stock'
import {
  erpCountInputFormatter,
  erpPriceInputFormatter,
  erpPriceMultiply,
  getSumValue
} from '@/utils'

const props = defineProps<{
  items: undefined
  disabled: false
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
  productId: [{ required: true, message: '产品不能为空', trigger: 'blur' }],
  productPrice: [{ required: true, message: '产品单价不能为空', trigger: 'blur' }],
  count: [{ required: true, message: '产品数量不能为空', trigger: 'blur' }]
})
const formRef = ref([]) // 表单 Ref
const productList = ref<ProductVO[]>([]) // 产品列表
const productCodeList = ref<ProductVO[]>([]) // 通用产品码列表
const filteredProductCodeLists = ref<Record<number, ProductVO[]>>({}) // 每行的过滤后产品码列表

/** 初始化设置入库项 */
watch(
  () => props.items,
  async (val) => {
    formData.value = val
  },
  { immediate: true }
)

/** 监听合同产品变化，计算合同产品总价 */
watch(
  () => formData.value,
  (val) => {
    if (!val || val.length === 0) {
      return
    }
    // 循环处理
    val.forEach((item) => {
      item.totalProductPrice = erpPriceMultiply(item.productPrice, item.count)
      item.taxPrice = erpPriceMultiply(item.totalProductPrice, item.taxPercent / 100.0)
      if (item.totalProductPrice != null) {
        item.totalPrice = item.totalProductPrice + (item.taxPrice || 0)
      } else {
        item.totalPrice = undefined
      }
    })
  },
  { deep: true }
)

/** 合计 */
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (['count', 'totalProductPrice', 'taxPrice', 'totalPrice'].includes(column.property)) {
      const sum = getSumValue(data.map((item) => Number(item[column.property])))
      sums[index] =
        column.property === 'count' ? erpCountInputFormatter(sum) : erpPriceInputFormatter(sum)
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    productId: undefined,
    productCode: undefined, // 通用产品码
    productUnitName: undefined, // 产品单位
    productBarCode: undefined, // 产品条码
    productPrice: undefined,
    stockCount: undefined,
    count: 1,
    totalProductPrice: undefined,
    taxPercent: undefined,
    taxPrice: undefined,
    totalPrice: undefined,
    remark: undefined
  }
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 处理产品变更 */
const onChangeProduct = (productId, row) => {
  const product = productList.value.find((item) => item.id === productId)
  if (product) {
    row.productCode = product.productCode
    row.productUnitName = product.unitName
    row.productBarCode = product.barCode
    row.productPrice = product.purchasePrice
  } else {
    // 如果清空产品，也清空通用产品码
    row.productCode = undefined
    row.productUnitName = undefined
    row.productBarCode = undefined
    row.productPrice = undefined
  }
  // 加载库存
  setStockCount(row)
}

/** 处理通用产品码变更 */
const onChangeProductCode = (productCode, row) => {
  const product = productList.value.find((item) => item.productCode === productCode)
  if (product) {
    row.productId = product.id
    row.productUnitName = product.unitName
    row.productBarCode = product.barCode
    row.productPrice = product.purchasePrice
  } else {
    // 如果清空通用产品码，也清空产品ID
    row.productId = undefined
    row.productUnitName = undefined
    row.productBarCode = undefined
    row.productPrice = undefined
  }
  // 加载库存
  setStockCount(row)
}

/** 加载库存 */
const setStockCount = async (row: any) => {
  if (!row.productId) {
    return
  }
  const count = await StockApi.getStockCount(row.productId)
  row.stockCount = count || 0
}

/** 自定义产品码过滤方法 */
const filterProductCode = (query: string, rowIndex: number) => {
  if (!query) {
    // 如果没有查询条件，显示所有有产品码的产品
    filteredProductCodeLists.value[rowIndex] = productCodeList.value.filter(item => item.productCode)
    return
  }

  // 只对产品码进行搜索匹配（大小写不敏感）
  const lowerQuery = query.toLowerCase()
  const filtered = productCodeList.value.filter(item => {
    return item.productCode && item.productCode.toLowerCase().includes(lowerQuery)
  })

  // 排序：完全匹配的排在前面，然后是前缀匹配，最后是包含匹配
  filtered.sort((a, b) => {
    const aCode = a.productCode?.toLowerCase() || ''
    const bCode = b.productCode?.toLowerCase() || ''

    // 完全匹配优先级最高
    if (aCode === lowerQuery && bCode !== lowerQuery) return -1
    if (aCode !== lowerQuery && bCode === lowerQuery) return 1

    // 前缀匹配优先级次之
    const aStartsWith = aCode.startsWith(lowerQuery)
    const bStartsWith = bCode.startsWith(lowerQuery)
    if (aStartsWith && !bStartsWith) return -1
    if (!aStartsWith && bStartsWith) return 1

    // 其他情况按字母顺序排序
    return aCode.localeCompare(bCode)
  })

  filteredProductCodeLists.value[rowIndex] = filtered
}

/** 获取指定行的过滤后产品码列表 */
const getFilteredProductCodeList = (rowIndex: number) => {
  return filteredProductCodeLists.value[rowIndex] || productCodeList.value.filter(item => item.productCode)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}
defineExpose({ validate })

/** 初始化 */
onMounted(async () => {
  productList.value = await ProductApi.getProductSimpleList()
  productCodeList.value = await ProductApi.getProductSimpleList() // 同时加载通用产品码列表

  // 初始化所有行的过滤列表（只显示有产品码的产品）
  const initialFilteredList = productCodeList.value.filter(item => item.productCode)
  formData.value.forEach((_, index) => {
    filteredProductCodeLists.value[index] = initialFilteredList
  })

  // 默认添加一个
  if (formData.value.length === 0) {
    handleAdd()
  }
})
</script>
